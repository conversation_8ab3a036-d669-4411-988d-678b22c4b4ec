<?php

declare(strict_types=1);

namespace P4dev\ElevePhpSdk;

use Dotenv\Dotenv;
use Saloon\Http\Connector;

class EleveConnector extends Connector
{
    public function __construct()
    {
        $this->loadEnvironmentVariables();
    }

    public function resolveBaseUrl(): string
    {
        $baseUrl = $_ENV['ELEVE_API_BASE_URL'] ?? getenv('ELEVE_API_BASE_URL');

        if (empty($baseUrl)) {
            throw new \InvalidArgumentException(
                'ELEVE_API_BASE_URL environment variable is not set. Please check your .env file.'
            );
        }

        return $baseUrl;
    }

    private function loadEnvironmentVariables(): void
    {
        // Try to find .env file starting from current directory and going up
        $paths = [
            getcwd(),
            dirname(__DIR__),
            dirname(dirname(__DIR__)),
            dirname(dirname(dirname(__DIR__))),
        ];

        foreach ($paths as $path) {
            if (file_exists($path . '/.env')) {
                $dotenv = Dotenv::createImmutable($path);
                $dotenv->safeLoad();
                return;
            }
        }

        // If no .env file is found, that's okay - environment variables might be set elsewhere
    }
}