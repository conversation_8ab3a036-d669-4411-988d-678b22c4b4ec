<?php

declare(strict_types=1);

namespace Saloon\Http\Auth;

use Saloon\Http\PendingRequest;
use <PERSON>oon\Contracts\Authenticator;

class BasicAuthenticator implements Authenticator
{
    /**
     * Constructor
     */
    public function __construct(
        public string $username,
        public string $password,
    ) {
        //
    }

    /**
     * Apply the authentication to the request.
     */
    public function set(PendingRequest $pendingRequest): void
    {
        $pendingRequest->headers()->add('Authorization', 'Basic ' . base64_encode($this->username . ':' . $this->password));
    }
}
