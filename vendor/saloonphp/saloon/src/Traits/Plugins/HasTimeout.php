<?php

declare(strict_types=1);

namespace Saloon\Traits\Plugins;

use <PERSON>oon\Config;
use GuzzleHttp\RequestOptions;
use Saloon\Http\PendingRequest;

/**
 * @phpstan-ignore trait.unused
 */
trait HasTimeout
{
    /**
     * Boot HasTimeout plugin.
     */
    public function bootHasTimeout(PendingRequest $pendingRequest): void
    {
        $pendingRequest->config()->merge([
            RequestOptions::CONNECT_TIMEOUT => $this->getConnectTimeout(),
            RequestOptions::TIMEOUT => $this->getRequestTimeout(),
        ]);
    }

    /**
     * Get the request connection timeout.
     */
    public function getConnectTimeout(): float
    {
        return property_exists($this, 'connectTimeout') ? $this->connectTimeout : Config::$defaultConnectionTimeout;
    }

    /**
     * Get the request timeout.
     */
    public function getRequestTimeout(): float
    {
        return property_exists($this, 'requestTimeout') ? $this->requestTimeout : Config::$defaultRequestTimeout;
    }
}
