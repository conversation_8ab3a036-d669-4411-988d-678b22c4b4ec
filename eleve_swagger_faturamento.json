{"openapi": "3.0.1", "info": {"title": "Docs - API's Faturamento", "contact": {"name": "Docs", "url": "https://gestao.meueleve.com.br/account/externallogin"}, "version": "1.0.0"}, "servers": [{"url": "https://gateway.fly01.com.br", "description": "Gateway"}], "paths": {"/api/v2/faturamento/adquirente": {"post": {"tags": ["Adquirente"], "summary": "Realiza o cadastro do adquirente.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Adquirente"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Adquirente"}}}}}}, "get": {"tags": ["Adquirente"], "summary": "Realiza a consulta dos adquirentes cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdquirenteODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/adquirente/{key}": {"put": {"tags": ["Adquirente"], "summary": "Realiza alteração no cadastro do adquirente.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Adquirente"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Adquirente"], "summary": "Exclui o adquirente cadastrado.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/armazem": {"post": {"tags": ["Armazem"], "summary": "Realiza o cadastro do armazém.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Armazem"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Armazem"}}}}}}, "get": {"tags": ["Armazem"], "summary": "Realiza a consulta dos armazéns cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArmazemODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/armazem/{key}": {"put": {"tags": ["Armazem"], "summary": "Realiza alteração no cadastro do armazém.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Armazem"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Armazem"], "summary": "Exclui o armazém cadastrado.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/CalculaTotalNotaFiscal": {"get": {"tags": ["CalculaTotalNotaFiscal"], "summary": "Realiza o cálculo do total da nota fiscal", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "notaFiscalId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/CalculaTotalOrdemVenda": {"get": {"tags": ["CalculaTotalOrdemVenda"], "summary": "Realiza o cálculo dos valores totais do pedido de venda.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "ordemVendaId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "clienteId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "geraNotaFiscal", "in": "query", "required": true, "schema": {"type": "boolean"}}, {"name": "somaFreteCif", "in": "query", "required": true, "schema": {"type": "boolean"}}, {"name": "tipoNfeComplementar", "in": "query", "schema": {"type": "string", "default": "NaoComplementar"}}, {"name": "tipoFrete", "in": "query", "schema": {"type": "string", "default": "SemFrete"}}, {"name": "valor<PERSON>rete", "in": "query", "schema": {"type": "number", "format": "double", "default": 0}}, {"name": "onList", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/categoria": {"post": {"tags": ["Categoria"], "summary": "Realiza o cadastro de uma nova categoria financeira.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Categoria"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Categoria"}}}}}}, "get": {"tags": ["Categoria"], "summary": "Realiza a consulta das categorias financeiras cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoriaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/categoria/{key}": {"put": {"tags": ["Categoria"], "summary": "Realiza alteração no cadastro da categoria financeira.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Categoria"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Categoria"], "summary": "Exclui a categoria financeira.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/centroCusto": {"post": {"tags": ["CentroCusto"], "summary": "Realiza o cadastro de um novo centro de custo.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}}}, "get": {"tags": ["CentroCusto"], "summary": "Realiza a consulta dos centros de custos cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCustoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/centroCusto/{key}": {"put": {"tags": ["CentroCusto"], "summary": "Realiza a alteração no cadastro de um centro de custo.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["CentroCusto"], "summary": "Exclui o centro de custo.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CentroCusto"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/certificadodigital": {"post": {"tags": ["CertificadoDigital"], "summary": "Realiza o Cadastro do CertificadoDigital", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}}}, "get": {"tags": ["CertificadoDigital"], "summary": "Realiza uma Consulta livre de CertificadoDigital", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigitalODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/certificadodigital/{key}": {"put": {"tags": ["CertificadoDigital"], "summary": "Realiza Alteração do CertificadoDigital", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["CertificadoDigital"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificadoDigital"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/cest": {"get": {"tags": ["Cest"], "summary": "Realiza uma Consulta livre de Cest", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CestODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/cfop": {"get": {"tags": ["Cfop"], "summary": "CFOP", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CFOPODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/cidade": {"get": {"tags": ["Cidade"], "summary": "Realiza a consulta das cidades cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CidadeODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/condicaoParcelamento": {"post": {"tags": ["CondicaoParcelamento"], "summary": "Realiza o cadastrado da condição de parcelamento.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}}}, "get": {"tags": ["CondicaoParcelamento"], "summary": "Realiza consulta no cadastro da condição de parcelamento.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamentoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/condicaoParcelamento/{key}": {"put": {"tags": ["CondicaoParcelamento"], "summary": "Realiza alteração no cadastro da condição de parcelamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["CondicaoParcelamento"], "summary": "Exclui a condição de parcelamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CondicaoParcelamento"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/departamento": {"post": {"tags": ["Departamento"], "summary": "Realiza o Cadastro do Departamento", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Departamento"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Departamento"}}}}}}, "get": {"tags": ["Departamento"], "summary": "Realiza uma Consulta livre de Departamento", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartamentoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/departamento/{key}": {"put": {"tags": ["Departamento"], "summary": "Realiza Alteração do Departamento", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Departamento"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Departamento"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/departamentosetor": {"post": {"tags": ["DepartamentoSetor"], "summary": "Realiza o Cadastro de Setor do Departamento", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartamentoSetor"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartamentoSetor"}}}}}}, "get": {"tags": ["DepartamentoSetor"], "summary": "Realiza uma Consulta livre de Setor do Departamento", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartamentoSetorODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/departamentosetor/{key}": {"put": {"tags": ["DepartamentoSetor"], "summary": "Realiza Alteração de Setor do Departamento", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartamentoSetor"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["DepartamentoSetor"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/Difal": {"post": {"tags": ["Difal"], "summary": "Realiza o Cadastro do Difal", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Difal"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Difal"}}}}}}, "get": {"tags": ["Difal"], "summary": "Realiza uma Consulta livre de Difal", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DifalODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/Difal/{key}": {"put": {"tags": ["Difal"], "summary": "Realiza Alteração do Difal", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Difal"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Difal"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/EnquadramentoLegalIPI": {"get": {"tags": ["EnquadramentoLegalIpi"], "summary": "Realiza a consulta dos enquadramentos legais de IPI cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnquadramentoLegalIPIODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/Estado": {"get": {"tags": ["Estado"], "summary": "Realiza a consulta dos estados cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EstadoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/formaPagamento": {"post": {"tags": ["FormaPagamento"], "summary": "Realiza o cadastro da forma de pagamento.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamento"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamento"}}}}}}, "get": {"tags": ["FormaPagamento"], "summary": "Realiza consulta nas formas de pagamento cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/formaPagamento/{key}": {"put": {"tags": ["FormaPagamento"], "summary": "Realiza alteração no cadastro da forma de pagamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoPut"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["FormaPagamento"], "summary": "Exclui o a forma de pagamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/FormaPagamentotaxacartao": {"post": {"tags": ["FormaPagamentoTaxaCartao"], "summary": "Realiza o cadastro do intervalo de taxas para forma de pagamento.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartao"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartao"}}}}}}, "get": {"tags": ["FormaPagamentoTaxaCartao"], "summary": "Realiza consulta dos intervalos de taxas para formas de pagamento.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartaoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/FormaPagamentotaxacartao/{key}": {"put": {"tags": ["FormaPagamentoTaxaCartao"], "summary": "Realiza alteração do intervalo de taxas para o cadastro da forma de pagamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartao"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["FormaPagamentoTaxaCartao"], "summary": "Exclui o intervalo de taxas da forma de pagamento.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/gerenciamentoformapagamentotaxacartao/associartaxas": {"post": {"tags": ["GerenciamentoFormaPagamentoTaxaCartao"], "summary": "Realiza a inclusão de taxas associadas à uma forma de pagamento.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GerenciamentoFormaPagamentoTaxaCartaoPost"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/gerenciamentoformapagamentotaxacartao/removerselecionadas": {"post": {"tags": ["GerenciamentoFormaPagamentoTaxaCartao"], "summary": "Realiza exclusão das taxas informadas.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GerenciamentoFormaPagamentoTaxaCartaoDelete"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupoProduto": {"post": {"tags": ["GrupoProduto"], "summary": "Realiza o cadastro do grupo de produto.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProduto"}}}}}}, "get": {"tags": ["GrupoProduto"], "summary": "Realiza consulta dos grupos de produtos cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProdutoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupoProduto/{key}": {"put": {"tags": ["GrupoProduto"], "summary": "Realiza alteração no cadastro do grupo de produto.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["GrupoProduto"], "summary": "Exclui o grupo de produto.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupoprodutopos": {"post": {"tags": ["GrupoProdutoPOS"], "summary": "Realiza o cadastro do grupo de produto.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProdutoPOS"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProdutoPOS"}}}}}}, "get": {"tags": ["GrupoProdutoPOS"], "summary": "Realiza consulta dos grupos de produtos cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProdutoPOSODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupoprodutopos/{key}": {"put": {"tags": ["GrupoProdutoPOS"], "summary": "Realiza alteração no cadastro do grupo de produto.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoProdutoPOS"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["GrupoProdutoPOS"], "summary": "Exclui o grupo de produto.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupostributarios": {"post": {"tags": ["GruposTributarios"], "summary": "Realiza o Cadastro do Grupo tributário", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GruposTributarios"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/v2/faturamento/grupotributario": {"post": {"tags": ["GrupoTributario"], "summary": "Realiza o Cadastro do Grupo Tributário", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributario"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributario"}}}}}}, "get": {"tags": ["GrupoTributario"], "summary": "Realiza uma Consulta livre de Parâmetros Tributários", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributarioODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupotributario/{key}": {"put": {"tags": ["GrupoTributario"], "summary": "Realiza Alteração do Grupo Tributário", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributario"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["GrupoTributario"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupotributarioproduto": {"post": {"tags": ["GrupoTributarioProduto"], "summary": "Realiza o Cadastro do vículo entre produto e grupo tributário", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributarioProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributarioProduto"}}}}}}, "get": {"tags": ["GrupoTributarioProduto"], "summary": "Realiza uma Consulta livre de Parâmetros Tributários", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributarioProdutoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/grupotributarioproduto/{key}": {"put": {"tags": ["GrupoTributarioProduto"], "summary": "Realiza Alteração do vículo entre produto e grupo tributário", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrupoTributarioProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["GrupoTributarioProduto"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/modificador": {"post": {"tags": ["Modificador"], "summary": "Realiza o cadastro do modificador.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Modificador"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Modificador"}}}}}}, "get": {"tags": ["Modificador"], "summary": "Realiza uma consulta de modificadores.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModificadorODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/modificador/{key}": {"put": {"tags": ["Modificador"], "summary": "Realiza a alteração do modificador.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Modificador"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Modificador"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ncm": {"post": {"tags": ["Ncm"], "summary": "Realiza o cadastro da NCM.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ncm"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ncm"}}}}}}, "get": {"tags": ["Ncm"], "summary": "Realiza a consulta das NCMs cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NcmODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ncm/{key}": {"put": {"tags": ["Ncm"], "summary": "Realiza alteração no cadastro da NCM.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ncm"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Ncm"], "summary": "Exclui o cadastro da NCM.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/nfe/{notaFiscalId}": {"put": {"tags": ["NFe"], "summary": "Realiza a transmissão da NFe.", "parameters": [{"name": "notaFiscalId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NFe"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NFeProduto": {"get": {"tags": ["NFeProduto"], "summary": "Realiza a consulta dos produtos que foram incluídos na NFe.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/nfse/{notaFiscalId}": {"put": {"tags": ["NFSe"], "summary": "Realiza a transmissão da NFSe.", "parameters": [{"name": "notaFiscalId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NFSe"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NFSeServico": {"get": {"tags": ["NFSeServico"], "summary": "Realiza a consulta dos serviços que foram incluídos na NFSe.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/notaFiscal": {"get": {"tags": ["NotaFiscal"], "summary": "Realiza a consulta das notas fiscais.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/notaFiscal/{key}": {"delete": {"tags": ["NotaFiscal"], "summary": "Exclui a nota fiscal.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NotaFiscalAtualizaStatus": {"get": {"tags": ["NotaFiscalAtualizaStatus"], "summary": "Realiza a atualização de status das notas fiscais", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NotaFiscalCancelar": {"post": {"tags": ["NotaFiscalCancelar"], "summary": "Realiza o cancelamento da nota fiscal.", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NotaFiscalCartaCorrecao": {"post": {"tags": ["NotaFiscalCartaCorrecao"], "summary": "Realiza o cadastro do evento de correção da NF-e.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartaCorrecao"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartaCorrecao"}}}}}}, "get": {"tags": ["NotaFiscalCartaCorrecao"], "summary": "Realiza a consulta dos eventos de correção das notas fiscais eletrônicas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartaCorrecaoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/NotaFiscalCartaCorrecaoAtualizaStatus": {"get": {"tags": ["NotaFiscalCartaCorrecaoAtualizaStatus"], "summary": "Realiza a atualização do status do evento de correção das notas fiscais eletrônicas.", "parameters": [{"name": "IdNotaFiscal", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/notafiscaltributacao/{id}": {"get": {"tags": ["NotaFiscalTributacao"], "summary": "Obtenha detalhes de impostos pagos na Nota Fisal.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVenda": {"post": {"tags": ["OrdemVenda"], "summary": "Realiza o cadastro do pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVenda"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVenda"}}}}}}, "get": {"tags": ["OrdemVenda"], "summary": "Realiza a consulta dos pedidos de venda.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVenda/{key}": {"put": {"tags": ["OrdemVenda"], "summary": "Realiza alteração no pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVenda"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["OrdemVenda"], "summary": "Exclui o pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVendaFormaPagamento": {"post": {"tags": ["OrdemVendaFormaPagamento"], "summary": "Realiza o cadastro da forma de pagamento para o pedido.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaFormaPagamentoPost"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaFormaPagamentoPost"}}}}}}, "get": {"tags": ["OrdemVendaFormaPagamento"], "summary": "Realiza a consulta da forma de pagamento associada ao pedido.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaFormaPagamentoPostODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVendaProduto": {"post": {"tags": ["OrdemVendaProduto"], "summary": "Realiza a inclusão do produto no pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaProduto"}}}}}}, "get": {"tags": ["OrdemVendaProduto"], "summary": "Realiza a consulta dos produtos que compõem o pedido de venda.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaProdutoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVendaProduto/{key}": {"put": {"tags": ["OrdemVendaProduto"], "summary": "Realiza alteração no produto que compõe o pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaProduto"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["OrdemVendaProduto"], "summary": "Exclui o produto do pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVendaservico": {"post": {"tags": ["OrdemVenda<PERSON><PERSON>"], "summary": "Realiza a inclusão do serviço no pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaServico"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaServico"}}}}}}, "get": {"tags": ["OrdemVenda<PERSON><PERSON>"], "summary": "Realiza a consulta dos serviços que compõem o pedido de venda.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaServicoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/ordemVendaservico/{key}": {"put": {"tags": ["OrdemVenda<PERSON><PERSON>"], "summary": "Realiza alteração no serviço que compõe o pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrdemVendaServico"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["OrdemVenda<PERSON><PERSON>"], "summary": "Exclui o serviço do pedido de venda.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/Pais": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Realiza a consulta dos países cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaisODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/parametrotributario": {"post": {"tags": ["ParametroTributario"], "summary": "Realiza o Cadastro do Parâmetro Tributário", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParametroTributario"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParametroTributario"}}}}}}, "get": {"tags": ["ParametroTributario"], "summary": "Realiza uma Consulta livre de Parâmetros Tributários", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParametroTributarioODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/parametrotributario/{key}": {"put": {"tags": ["ParametroTributario"], "summary": "Realiza Alteração do Parâmetro Tributário", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParametroTributario"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["ParametroTributario"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/pessoa": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza o cadastro da pessoa.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pessoa"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pessoa"}}}}}}, "get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza a consulta das pessoas cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PessoaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/pessoa/{key}": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza alteração no cadastro da pessoa.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pessoa"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Exclui o cadastro da pessoa.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/planoConta": {"post": {"tags": ["PlanoConta"], "summary": "Realiza o cadastro da plano de contas.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanoConta"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanoConta"}}}}}}, "get": {"tags": ["PlanoConta"], "summary": "Realiza a consulta dos planos de conta cadastrados.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanoContaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/planoConta/{key}": {"put": {"tags": ["PlanoConta"], "summary": "Realiza alteração no cadastro da plano de contas.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanoConta"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["PlanoConta"], "summary": "Exclui o cadastro de  planode conta.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/produto": {"post": {"tags": ["Produ<PERSON>"], "summary": "Realiza o Cadastro do Produto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoPostGet"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoPostGet"}}}}}}, "get": {"tags": ["Produ<PERSON>"], "summary": "Realiza uma Consulta livre de Produto", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoPostGetODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/produto/{key}": {"put": {"tags": ["Produ<PERSON>"], "summary": "Realiza Alteração do Produto", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoBase"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["Produ<PERSON>"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/produtos": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza o Cadastro do Produto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Produtos"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/v2/faturamento/reabrirpedido": {"post": {"tags": ["ReabrirPedido"], "summary": "Realiza a reabertura de um pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReabrirPedido"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReabrirPedido"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/serieNotaFiscal": {"post": {"tags": ["SerieNotaFiscal"], "summary": "Realiza o Cadastro do SerieNotaFiscal", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SerieNotaFiscal"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SerieNotaFiscal"}}}}}}, "get": {"tags": ["SerieNotaFiscal"], "summary": "Realiza uma Consulta livre de SerieNotaFiscal", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SerieNotaFiscalODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/serieNotaFiscal/{key}": {"put": {"tags": ["SerieNotaFiscal"], "summary": "Realiza Alteração do SerieNotaFiscal", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SerieNotaFiscal"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["SerieNotaFiscal"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/servico": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza o cadastro do serviço.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ser<PERSON>o"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ser<PERSON>o"}}}}}}, "get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza a consulta no cadastro do serviço.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/servico/{key}": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza alteração no cadastro do serviço.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ser<PERSON>o"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Exclui o serviço.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/servicopdv": {"post": {"tags": ["ServicoPDV"], "summary": "Realiza o cadastro do serviço para pontos de vendas.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicoPDV"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicoPDV"}}}}}}, "get": {"tags": ["ServicoPDV"], "summary": "Realiza uma consulta de serviço de ponto de venda.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicoPDVODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/servicopdv/{key}": {"put": {"tags": ["ServicoPDV"], "summary": "Realiza a alteração do serviço de PDV.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicoPDV"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["ServicoPDV"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/substituicaotributaria": {"post": {"tags": ["SubstituicaoTributaria"], "summary": "Realiza o Cadastro do Substituição Tributária", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstituicaoTributaria"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstituicaoTributaria"}}}}}}, "get": {"tags": ["SubstituicaoTributaria"], "summary": "Realiza uma Consulta livre de Substituição Tributária", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstituicaoTributariaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/substituicaotributaria/{key}": {"put": {"tags": ["SubstituicaoTributaria"], "summary": "Realiza Alteração do Substituição Tributária", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstituicaoTributaria"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["SubstituicaoTributaria"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/tabelaPreco": {"post": {"tags": ["TabelaPreco"], "summary": "Realiza o cadastro da tabela de preço.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TabelaPreco"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TabelaPreco"}}}}}}, "get": {"tags": ["TabelaPreco"], "summary": "Realiza a consulta das tabelas de preço cadastradas.", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TabelaPrecoODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/tabelaPreco/{key}": {"put": {"tags": ["TabelaPreco"], "summary": "Realiza alteração no cadastro da tabela de preço.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TabelaPreco"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["TabelaPreco"], "summary": "Exclui a tabela de preço.", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/unidadeMedida": {"post": {"tags": ["UnidadeMedida"], "summary": "Realiza o Cadastro da Unidade de Medida", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnidadeMedida"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnidadeMedida"}}}}}}, "get": {"tags": ["UnidadeMedida"], "summary": "Realiza uma Consulta livre de UnidadeMedida", "description": "Para maiores informações acesse: https://docs.microsoft.com/pt-br/graph/query-parameters\n A utilização do prefixo Fly01.Core.Entities.Domains.Enum. é necessária para todos os filtros envolvendo enums. Exemplo: Fly01.Core.Entities.Domains.Enum.Status'Aberto'", "parameters": [{"name": "$filter", "in": "query", "description": "id eq 32732080-6af2-4ry3-9c84-1d18a34fa60d", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "Ordena o resultado usando sintaxe OData", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "Número de resultados para pular usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$top", "in": "query", "description": "Filtra o número de dados usando sintaxe OData", "schema": {"type": "integer"}}, {"name": "$count", "in": "query", "description": "Retorna o total de dados usando sintaxe OData", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnidadeMedidaODataListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/unidadeMedida/{key}": {"put": {"tags": ["UnidadeMedida"], "summary": "Realiza Alteração da Unidade de Medida", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnidadeMedida"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}, "delete": {"tags": ["UnidadeMedida"], "summary": "Exclui o registro da base", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/venda": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza o cadastro do pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Venda"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendaResponse"}}}}}}, "put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza alteração no pedido de venda.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Venda"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendaResponse"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Exclui o pedido de venda.", "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "numero", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}}}}, "/api/v2/faturamento/venda/atualizarImpostos": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza a atualização dos impostos dos produtos.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendaAtualizacaoImpostos"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendaResponse"}}}}}}}, "/api/v2/faturamento/vendas": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Realiza o cadastro de pedidos de venda em massa.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Vendas"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInnerMessage"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseError"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendasResponse"}}}}}}}}, "components": {"schemas": {"Adquirente": {"required": ["razaoSocial"], "type": "object", "properties": {"razaoSocial": {"minLength": 1, "type": "string"}, "cnpj": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AdquirenteODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Adquirente"}, "nullable": true}}, "additionalProperties": false}, "Armazem": {"required": ["codigo", "descricao"], "type": "object", "properties": {"codigo": {"minLength": 1, "type": "string"}, "descricao": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ArmazemFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ArmazemODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Armazem"}, "nullable": true}}, "additionalProperties": false}, "Autenticacao": {"required": ["Password", "PlataformaId", "UserName"], "type": "object", "properties": {"UserName": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string"}, "PlataformaId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CFOP": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}, "descricao": {"type": "string", "nullable": true}, "tipo": {"enum": ["Entrada", "<PERSON><PERSON>"], "type": "string"}}, "additionalProperties": false}, "CFOPFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CFOPODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/CFOP"}, "nullable": true}}, "additionalProperties": false}, "CartaCorrecao": {"type": "object", "properties": {"producao": {"type": "string", "nullable": true}, "homologacao": {"type": "string", "nullable": true}, "entidadeAmbiente": {"enum": ["Con<PERSON>gu<PERSON><PERSON>", "Producao", "Homologacao"], "type": "string"}, "entidadeAmbienteNFS": {"enum": ["Con<PERSON>gu<PERSON><PERSON>", "Producao", "Homologacao"], "type": "string"}, "versaoTSS": {"enum": ["v278", "v300"], "type": "string"}, "entidadeTSS": {"type": "string", "nullable": true, "readOnly": true}, "sefazChaveAcesso": {"type": "string", "nullable": true}, "correcao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CartaCorrecaoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/CartaCorrecao"}, "nullable": true}}, "additionalProperties": false}, "Categoria": {"required": ["descricao", "tipoCarteira"], "type": "object", "properties": {"descricao": {"minLength": 1, "type": "string"}, "categoriaPaiId": {"type": "string", "format": "uuid", "nullable": true}, "planoContaId": {"type": "string", "format": "uuid", "nullable": true}, "tipoCarteira": {"enum": ["<PERSON><PERSON><PERSON>", "Despesa"], "type": "string"}}, "additionalProperties": false}, "CategoriaFiltro": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CategoriaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Categoria"}, "nullable": true}}, "additionalProperties": false}, "CentroCusto": {"required": ["codigo", "descricao"], "type": "object", "properties": {"codigo": {"minLength": 1, "type": "string"}, "descricao": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CentroCustoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/CentroCusto"}, "nullable": true}}, "additionalProperties": false}, "CertificadoDigital": {"required": ["certificado", "md5"], "type": "object", "properties": {"tipo": {"type": "integer", "format": "int32"}, "dataEmissao": {"type": "string", "format": "date-time"}, "dataExpiracao": {"type": "string", "format": "date-time"}, "versao": {"type": "string", "nullable": true}, "certificado": {"minLength": 1, "type": "string"}, "senha": {"type": "string", "nullable": true}, "entidadeHomologacao": {"type": "string", "nullable": true}, "entidadeProducao": {"type": "string", "nullable": true}, "emissor": {"type": "string", "nullable": true}, "pessoa": {"type": "string", "nullable": true}, "md5": {"maxLength": 32, "minLength": 1, "type": "string"}, "cnpj": {"maxLength": 16, "type": "string", "nullable": true}, "inscricaoEstadual": {"maxLength": 18, "type": "string", "nullable": true}, "uf": {"maxLength": 2, "type": "string", "nullable": true}}, "additionalProperties": false}, "CertificadoDigitalODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/CertificadoDigital"}, "nullable": true}}, "additionalProperties": false}, "Cest": {"required": ["codigo", "descricao"], "type": "object", "properties": {"codigo": {"minLength": 1, "type": "string"}, "descricao": {"maxLength": 650, "minLength": 1, "type": "string"}, "segmento": {"type": "string", "nullable": true}, "item": {"type": "string", "nullable": true}, "anexo": {"type": "string", "nullable": true}, "ncmId": {"type": "string", "format": "uuid", "nullable": true}, "Ncm": {"$ref": "#/components/schemas/NcmFiltro"}}, "additionalProperties": false}, "CestFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CestODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cest"}, "nullable": true}}, "additionalProperties": false}, "Cidade": {"required": ["codigoIbge", "estadoId", "nome"], "type": "object", "properties": {"nome": {"maxLength": 35, "minLength": 0, "type": "string"}, "codigoIbge": {"maxLength": 7, "minLength": 0, "type": "string"}, "estadoId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CidadeFiltro": {"type": "object", "properties": {"nome": {"maxLength": 35, "minLength": 0, "type": "string", "nullable": true}, "codigoIbge": {"maxLength": 7, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CidadeODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cidade"}, "nullable": true}}, "additionalProperties": false}, "CondicaoParcelamento": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"minLength": 1, "type": "string"}, "qtdParcelas": {"type": "integer", "format": "int32", "nullable": true}, "condicoesParcelamento": {"type": "string", "nullable": true}, "modoUtilizacao": {"enum": ["Ambos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContaPagar"], "type": "string"}}, "additionalProperties": false}, "CondicaoParcelamentoFiltro": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CondicaoParcelamentoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/CondicaoParcelamento"}, "nullable": true}}, "additionalProperties": false}, "Departamento": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}, "descricao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DepartamentoFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DepartamentoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Departamento"}, "nullable": true}}, "additionalProperties": false}, "DepartamentoSetor": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}, "descricao": {"type": "string", "nullable": true}, "departamentoId": {"type": "string", "format": "uuid"}, "departamento": {"$ref": "#/components/schemas/DepartamentoFiltro"}}, "additionalProperties": false}, "DepartamentoSetorFiltro": {"type": "object", "properties": {"Codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DepartamentoSetorODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/DepartamentoSetor"}, "nullable": true}}, "additionalProperties": false}, "Difal": {"type": "object", "properties": {"ncmId": {"type": "string", "format": "uuid"}, "estadoOrigemId": {"type": "string", "format": "uuid"}, "estadoDestinoId": {"type": "string", "format": "uuid"}, "tipoEntradaSaida": {"enum": ["Entrada", "<PERSON><PERSON>"], "type": "string"}, "cestId": {"type": "string", "format": "uuid", "nullable": true}, "fcp": {"type": "number", "format": "double"}, "aliquotaIntraEstadual": {"type": "number", "format": "double"}, "aliquotaInterEstadual": {"type": "number", "format": "double"}}, "additionalProperties": false}, "DifalODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Difal"}, "nullable": true}}, "additionalProperties": false}, "EnquadramentoLegalIPI": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}, "grupoCST": {"type": "string", "nullable": true}, "descricao": {"maxLength": 600, "type": "string", "nullable": true}}, "additionalProperties": false}, "EnquadramentoLegalIPIFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EnquadramentoLegalIPIODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/EnquadramentoLegalIPI"}, "nullable": true}}, "additionalProperties": false}, "Error": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "innerError": {"$ref": "#/components/schemas/InnerError"}}, "additionalProperties": false}, "Estado": {"required": ["nome", "sigla", "utcId"], "type": "object", "properties": {"sigla": {"maxLength": 2, "minLength": 0, "type": "string"}, "nome": {"maxLength": 20, "minLength": 0, "type": "string"}, "utcId": {"maxLength": 35, "minLength": 0, "type": "string"}, "codigoIbge": {"maxLength": 2, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "EstadoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Estado"}, "nullable": true}}, "additionalProperties": false}, "FormaPagamento": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"minLength": 1, "type": "string"}, "tipoFormaPagamento": {"enum": ["<PERSON><PERSON><PERSON>", "Cheque", "CartaoCredito", "CartaoDebito", "CreditoLoja", "Transferencia", "ValeAlimentacao", "ValeRefeicao", "ValePresente", "ValeCombustivel", "DuplicataMercantil", "Boleto", "DepositoBancario", "Pix", "TransferenciaBancaria", "Cashback", "SemPagamento", "Outros"], "type": "string"}, "modoUtilizacao": {"enum": ["Ambos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContaPagar"], "type": "string"}, "adquirenteId": {"type": "string", "format": "uuid", "nullable": true}, "recebimentoUnico": {"type": "boolean"}, "taxas": {"type": "array", "items": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartaoFiltro"}, "nullable": true}}, "additionalProperties": false}, "FormaPagamentoFiltro": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}, "tipo": {"enum": ["<PERSON><PERSON><PERSON>", "Cheque", "CartaoCredito", "CartaoDebito", "CreditoLoja", "Transferencia", "ValeAlimentacao", "ValeRefeicao", "ValePresente", "ValeCombustivel", "DuplicataMercantil", "Boleto", "DepositoBancario", "Pix", "TransferenciaBancaria", "Cashback", "SemPagamento", "Outros"], "type": "string"}}, "additionalProperties": false}, "FormaPagamentoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/FormaPagamento"}, "nullable": true}}, "additionalProperties": false}, "FormaPagamentoPut": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"minLength": 1, "type": "string"}, "tipoFormaPagamento": {"enum": ["<PERSON><PERSON><PERSON>", "Cheque", "CartaoCredito", "CartaoDebito", "CreditoLoja", "Transferencia", "ValeAlimentacao", "ValeRefeicao", "ValePresente", "ValeCombustivel", "DuplicataMercantil", "Boleto", "DepositoBancario", "Pix", "TransferenciaBancaria", "Cashback", "SemPagamento", "Outros"], "type": "string"}, "modoUtilizacao": {"enum": ["Ambos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContaPagar"], "type": "string"}, "adquirenteId": {"type": "string", "format": "uuid", "nullable": true}, "recebimentoUnico": {"type": "boolean"}}, "additionalProperties": false}, "FormaPagamentoTaxaCartao": {"required": ["bandeira", "formaPagamentoId", "taxaUnica"], "type": "object", "properties": {"parcelaDe": {"type": "integer", "format": "int32", "nullable": true}, "parcelaAte": {"type": "integer", "format": "int32", "nullable": true}, "taxaTransacao": {"type": "number", "format": "double"}, "bandeira": {"enum": ["NaoInformado", "Visa", "Mastercard", "AmericanExpress", "Sorocred", "DinersClub", "Elo", "<PERSON><PERSON><PERSON>", "<PERSON>ra", "Cabal", "<PERSON><PERSON>", "BanesCard", "CalCard", "Credz", "Discover", "GoodCard", "GreenCard", "<PERSON><PERSON>", "JcB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Policard", "<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "Sodexo", "ValeCard", "Verocheque", "VR", "Ticket", "Outros"], "type": "string"}, "taxaUnica": {"type": "boolean"}, "diasRecebimento": {"type": "integer", "format": "int32"}, "valorTaxaMinima": {"type": "number", "format": "double"}, "formaPagamentoId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "FormaPagamentoTaxaCartaoBase": {"required": ["bandeira", "taxaUnica"], "type": "object", "properties": {"parcelaDe": {"type": "integer", "format": "int32", "nullable": true}, "parcelaAte": {"type": "integer", "format": "int32", "nullable": true}, "taxaTransacao": {"type": "number", "format": "double"}, "bandeira": {"enum": ["NaoInformado", "Visa", "Mastercard", "AmericanExpress", "Sorocred", "DinersClub", "Elo", "<PERSON><PERSON><PERSON>", "<PERSON>ra", "Cabal", "<PERSON><PERSON>", "BanesCard", "CalCard", "Credz", "Discover", "GoodCard", "GreenCard", "<PERSON><PERSON>", "JcB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Policard", "<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "Sodexo", "ValeCard", "Verocheque", "VR", "Ticket", "Outros"], "type": "string"}, "taxaUnica": {"type": "boolean"}, "diasRecebimento": {"type": "integer", "format": "int32"}, "valorTaxaMinima": {"type": "number", "format": "double"}}, "additionalProperties": false}, "FormaPagamentoTaxaCartaoFiltro": {"required": ["bandeira", "formaPagamentoId", "taxaUnica"], "type": "object", "properties": {"formaPagamentoId": {"type": "string", "format": "uuid"}, "parcelaDe": {"type": "integer", "format": "int32", "nullable": true}, "parcelaAte": {"type": "integer", "format": "int32", "nullable": true}, "taxaTransacao": {"type": "number", "format": "double"}, "bandeira": {"enum": ["NaoInformado", "Visa", "Mastercard", "AmericanExpress", "Sorocred", "DinersClub", "Elo", "<PERSON><PERSON><PERSON>", "<PERSON>ra", "Cabal", "<PERSON><PERSON>", "BanesCard", "CalCard", "Credz", "Discover", "GoodCard", "GreenCard", "<PERSON><PERSON>", "JcB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Policard", "<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "Sodexo", "ValeCard", "Verocheque", "VR", "Ticket", "Outros"], "type": "string"}, "taxaUnica": {"type": "boolean"}, "diasRecebimento": {"type": "integer", "format": "int32"}, "valorTaxaMinima": {"type": "number", "format": "double"}}, "additionalProperties": false}, "FormaPagamentoTaxaCartaoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartao"}, "nullable": true}}, "additionalProperties": false}, "GerenciamentoFormaPagamentoTaxaCartaoDelete": {"required": ["taxas"], "type": "object", "properties": {"taxas": {"type": "array", "items": {"$ref": "#/components/schemas/ListaTaxasExclusao"}}}, "additionalProperties": false}, "GerenciamentoFormaPagamentoTaxaCartaoPost": {"required": ["formaPagamentoId", "taxas"], "type": "object", "properties": {"formaPagamentoId": {"type": "string", "format": "uuid"}, "taxas": {"type": "array", "items": {"$ref": "#/components/schemas/FormaPagamentoTaxaCartaoBase"}}}, "additionalProperties": false}, "GrupoProduto": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}, "aliquotaIpi": {"type": "number", "format": "double"}, "tipoProduto": {"enum": ["Insumo", "ProdutoFinal", "<PERSON><PERSON><PERSON>", "Outros", "ProdutoIntermediario", "MateriaPrima", "<PERSON><PERSON><PERSON>"], "type": "string"}, "unidadeMedidaId": {"type": "string", "format": "uuid", "nullable": true}, "ncmId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "GrupoProdutoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProduto"}, "nullable": true}}, "additionalProperties": false}, "GrupoProdutoPOS": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}, "grupoFixo": {"enum": ["Ali<PERSON><PERSON>", "Bebidas", "BebidasAlcoolicas", "Vinhos", "Outros", "<PERSON><PERSON><PERSON>"], "type": "string"}, "tipoGrupo": {"enum": ["<PERSON><PERSON><PERSON>", "Insumo", "<PERSON><PERSON><PERSON>"], "type": "string"}, "hierarquiaGrupo": {"enum": ["Grupo", "Subgrupo"], "type": "string"}, "ordenacao": {"type": "integer", "format": "int32"}, "disponivel": {"type": "boolean"}, "codigoIntegracaoContabil": {"type": "string", "nullable": true}, "icone": {"type": "string", "nullable": true}, "corGrupo": {"type": "string", "nullable": true}, "produtosAssociados": {"type": "array", "items": {"$ref": "#/components/schemas/ProdutoPOS"}, "nullable": true}, "subGrupos": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProdutoPOS"}, "nullable": true}}, "additionalProperties": false}, "GrupoProdutoPOSODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProdutoPOS"}, "nullable": true}}, "additionalProperties": false}, "GrupoTributario": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}, "cfopId": {"type": "string", "format": "uuid", "nullable": true}, "calculaIcms": {"type": "boolean"}, "tipoTributacaoICMS": {"enum": ["0", "10", "20", "30", "40", "41", "50", "51", "60", "70", "90", "101", "102", "103", "201", "202", "203", "300", "400", "500", "900"], "type": "string"}, "diferimentoAliquota": {"type": "number", "format": "double", "nullable": true}, "calculaIcmsDifal": {"type": "boolean"}, "aplicaIpiBaseIcms": {"type": "boolean"}, "aplicaFreteBaseIcms": {"type": "boolean"}, "aplicaDespesaBaseIcms": {"type": "boolean"}, "calculaIpi": {"type": "boolean"}, "tipoTributacaoIPI": {"enum": ["0 => Entrada com Recuperação de Crédito", "1 => Entrada Tributada com Alíquota Zero", "2 => Entrada Isenta", "3 => Entrada Não Tributada", "4 => Entrada Imune", "5 => Entrada Com Suspensão", "49 => Outras Entradas", "50 => Saída Tributada", "51 => Saída Tributável com Alíquota Zero", "52 => <PERSON><PERSON><PERSON>", "53 => Saída Não Tributada", "54 => <PERSON><PERSON><PERSON>", "55 => Saída com Suspensão", "99 => Outras Saídas"], "type": "string"}, "aplicaFreteBaseIpi": {"type": "boolean"}, "aplicaDespesaBaseIpi": {"type": "boolean"}, "ipiDevol": {"type": "boolean"}, "calculaPis": {"type": "boolean"}, "subtraiIcmsPis": {"type": "boolean"}, "retemPis": {"type": "boolean"}, "tipoTributacaoPIS": {"enum": ["1 => Operação Tributável com Alíquota Básica", "2 => Operação Tributável com Alíquota Diferenciada", "3 => Operação Tributável com Alíquota por Unidade de Medida de Produto", "4 => Operação Tributável Monofásica - Revenda a Alíquota Zero", "5 => Operação Tributável por Substituição Tributária", "6 => Operação Tributável a Alíquota Zero", "7 => Operação Isenta da Contribuição", "8 => Operação sem Incidência da Contribuição", "9 => Operação com Suspensão da Contribuição", "49 => Outras Operações de Saída", "50 => Operação com Direito a Crédito - Vinculada Exclusivamente a Receita Tributada no Mercado Interno", "51 => Operação com Direito a Crédito – Vinculada Exclusivamente a Receita Não Tributada no Mercado Interno", "52 => Operação com Direito a Crédito - Vinculada Exclusivamente a Receita de Exportação", "53 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno", "54 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas no Mercado Interno e de Exportação", "55 => Operação com Direito a Crédito - Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação", "56 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação", "60 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Tributada no Mercado Interno", "61 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Não-Tributada no Mercado Interno", "62 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita de Exportação", "63 => Crédito <PERSON>sumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno", "64 => Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas no Mercado Interno e de Exportação", "65 => Crédito Presumido - Operação de Aquisição Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação", "66 => Crédito <PERSON>sumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação", "67 => Crédit<PERSON> - Outras Operações", "70 => Operação de Aquisição sem Direito a Crédito", "71 => Operação de Aquisição com Isenção", "72 => Operação de Aquisição com Suspensão", "73 => Operação de Aquisição a Alíquota Zero", "74 => Operação de Aquisição sem Incidência da Contribuição", "75 => Operação de Aquisição por Substituição Tributária", "98 => Outras Operações de Entrada", "99 => Outras Operações"], "type": "string"}, "aplicaFreteBasePis": {"type": "boolean"}, "aplicaDespesaBasePis": {"type": "boolean"}, "calculaCofins": {"type": "boolean"}, "subtraiIcmsCofins": {"type": "boolean"}, "retemCofins": {"type": "boolean"}, "tipoTributacaoCOFINS": {"enum": ["1 => Operação Tributável com Alíquota Básica", "2 => Operação Tributável com Alíquota Diferenciada", "3 => Operação Tributável com Alíquota por Unidade de Medida de Produto", "4 => Operação Tributável Monofásica - Revenda a Alíquota Zero", "5 => Operação Tributável por Substituição Tributária", "6 => Operação Tributável a Alíquota Zero", "7 => Operação Isenta da Contribuição", "8 => Operação sem Incidência da Contribuição", "9 => Operação com Suspensão da Contribuição", "49 => Outras Operações de Saída", "50 => Operação com Direito a Crédito - Vinculada Exclusivamente a Receita Tributada no Mercado Interno", "51 => Operação com Direito a Crédito – Vinculada Exclusivamente a Receita Não Tributada no Mercado Interno", "52 => Operação com Direito a Crédito - Vinculada Exclusivamente a Receita de Exportação", "53 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno", "54 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas no Mercado Interno e de Exportação", "55 => Operação com Direito a Crédito - Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação", "56 => Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação", "60 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Tributada no Mercado Interno", "61 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Não-Tributada no Mercado Interno", "62 => Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita de Exportação", "63 => Crédito <PERSON>sumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno", "64 => Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas no Mercado Interno e de Exportação", "65 => Crédito Presumido - Operação de Aquisição Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação", "66 => Crédito <PERSON>sumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação", "67 => Crédit<PERSON> - Outras Operações", "70 => Operação de Aquisição sem Direito a Crédito", "71 => Operação de Aquisição com Isenção", "72 => Operação de Aquisição com Suspensão", "73 => Operação de Aquisição a Alíquota Zero", "74 => Operação de Aquisição sem Incidência da Contribuição", "75 => Operação de Aquisição por Substituição Tributária", "98 => Outras Operações de Entrada", "99 => Outras Operações"], "type": "string"}, "aplicaFreteBaseCofins": {"type": "boolean"}, "aplicaDespesaBaseCofins": {"type": "boolean"}, "calculaIss": {"type": "boolean"}, "retemISS": {"type": "boolean"}, "tipoTributacaoISS": {"enum": ["1 => Tributada integralmente", "2 => Tributada integralmente e com o ISSQN retido na fonte", "3 => Tributada integralmente e sujeita ao regime da substituição tributária", "4 => Tributada integralmente e com o ISSQN retido anteriormente pelo substituto tributário", "5 => Tributada com redução da base de cálculo", "6 => Tributada com redução da base de cálculo e com o ISSQN retido na fonte", "7 => Tributada com redução da base de cálculo e sujeita ao regime da substituição tributária", "8 => ributada com redução da base de cálculo e com o ISSQN retido anteriormente pelo substituto tributário", "9 => <PERSON><PERSON><PERSON> ou <PERSON>", "10 => Não tributada"], "type": "string"}, "tipoPagamentoImpostoISS": {"enum": ["1 => Dentro do Município", "2 => Fora do Município", "3 => Isenção", "4 => I<PERSON>ne"], "type": "integer", "format": "int32"}, "tipoCFPS": {"enum": ["1 => Para Tomador", "2 => Para bem de 3º por conta do Tomador"], "type": "string"}, "calculaSubstituicaoTributaria": {"type": "boolean"}, "aplicaFreteBaseST": {"type": "boolean"}, "aplicaDespesaBaseST": {"type": "boolean"}, "aplicaIpiBaseST": {"type": "boolean"}, "calculaCSLL": {"type": "boolean"}, "retemCSLL": {"type": "boolean"}, "calculaINSS": {"type": "boolean"}, "retemINSS": {"type": "boolean"}, "calculaImpostoRenda": {"type": "boolean"}, "retemImpostoRenda": {"type": "boolean"}, "codigoBeneficioFiscal": {"type": "string", "nullable": true}, "motivoDesoneracaoICMS": {"enum": ["1 => Táxi", "2 => Deficiente Físico", "3 => Produtor <PERSON>", "4 => Florista Locadora", "5 => Diplomatico/Consular", "6 => Motocicleta da Amazonia Ocidental", "7 => SUFRAMA", "8 => Venda <PERSON> Públicos", "9 => Outros"], "type": "string"}, "aplicaReducaoBaseICMS": {"type": "boolean"}, "subtraiValorDesonerado": {"type": "boolean"}, "aplicaReducaoBaseICMSST": {"type": "boolean"}, "calculaICMSPorDentro": {"type": "boolean"}, "aliquotaICMSPorDentro": {"type": "number", "format": "double"}, "tipoSequenciaCalculoICMS": {"enum": ["1 =>  1º Redução na base, 2º Cálculo por dentro", "2 =>  1º Cálculo por dentro, 2º Redução na base"], "type": "string"}, "calculaICMSSTPorDentro": {"type": "boolean"}, "aliquotaICMSSTPorDentro": {"type": "number", "format": "double"}, "tipoSequenciaCalculoST": {"enum": ["1 =>  1º Redução na base, 2º Cálculo por dentro", "2 =>  1º Cálculo por dentro, 2º Redução na base"], "type": "string"}, "tipoCalculoPorDentroST": {"enum": ["1 =>  Valor de mercadoria - Valor de ICMS / 1 - Aliquota de destino ", "2 =>  Valor de mercadoria / 1 - Aliquota de destino "], "type": "string"}, "calculaICMSDifalPorDentro": {"type": "boolean"}, "aliquotaICMSDifalPorDentro": {"type": "number", "format": "double"}, "aplicaIpiBaseIcmsDifal": {"type": "boolean"}, "aplicaFreteBaseIcmsDifal": {"type": "boolean"}, "aplicaDespesaBaseIcmsDifal": {"type": "boolean"}, "tipoSequenciaCalculoDifal": {"enum": ["1 =>  1º Redução na base, 2º Cálculo por dentro", "2 =>  1º Cálculo por dentro, 2º Redução na base"], "type": "string"}, "percentualReducaoBaseCalculoDifal": {"type": "number", "format": "double"}, "tipoCalculoPorDentroDifal": {"enum": ["1 =>  Valor de mercadoria - Valor de ICMS / 1 - Aliquota de destino ", "2 =>  Valor de mercadoria / 1 - Aliquota de destino "], "type": "string"}, "percentualReducaoBaseCalculo": {"type": "number", "format": "double"}, "tipoCalculoIcmsStRetido": {"enum": ["ValorMedio", "UltimoValor"], "type": "string"}, "percentualReducaoBaseCalculoST": {"type": "number", "format": "double"}, "aliquotaPISPASEP": {"type": "number", "format": "double"}, "aliquotaCOFINS": {"type": "number", "format": "double"}, "aliquotaICMS": {"type": "number", "format": "double"}, "aliquotaIPI": {"type": "number", "format": "double"}, "aliquotaFCP": {"type": "number", "format": "double"}, "aliquotaISS": {"type": "number", "format": "double"}, "aliquotaINSS": {"type": "number", "format": "double"}, "aliquotaImpostoRenda": {"type": "number", "format": "double"}, "aliquotaCSLL": {"type": "number", "format": "double"}, "cfop": {"$ref": "#/components/schemas/CFOPFiltro"}}, "additionalProperties": false}, "GrupoTributarioFiltro": {"type": "object", "properties": {"descricao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GrupoTributarioODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoTributario"}, "nullable": true}}, "additionalProperties": false}, "GrupoTributarioProduto": {"type": "object", "properties": {"grupoTributarioId": {"type": "string", "format": "uuid"}, "produtoId": {"type": "string", "format": "uuid"}, "grupoTributario": {"$ref": "#/components/schemas/GrupoTributarioFiltro"}, "produto": {"$ref": "#/components/schemas/ProdutoFiltro"}}, "additionalProperties": false}, "GrupoTributarioProdutoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoTributarioProduto"}, "nullable": true}}, "additionalProperties": false}, "GruposTributarios": {"required": ["itens"], "type": "object", "properties": {"itens": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoTributario"}}}, "additionalProperties": false}, "InnerError": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "stacktrace": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ListaTaxasExclusao": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "LoteFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Modificador": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"maxLength": 180, "minLength": 0, "type": "string"}, "quantidadeMinima": {"type": "number", "format": "double"}, "quantidadeMaxima": {"type": "number", "format": "double"}, "obrigatorio": {"type": "boolean"}, "disponivelFuturosPDVs": {"type": "boolean"}, "selecaoUnica": {"type": "boolean"}, "idThex": {"type": "string", "nullable": true}, "servicosPDV": {"type": "array", "items": {"$ref": "#/components/schemas/ModificadorServicoPDV"}, "nullable": true}, "itensComposicao": {"type": "array", "items": {"$ref": "#/components/schemas/ModificadorComposicao"}, "nullable": true}}, "additionalProperties": false}, "ModificadorComposicao": {"type": "object", "properties": {"produtoId": {"type": "string", "format": "uuid", "nullable": true}, "descricao": {"type": "string", "nullable": true}, "desconto": {"type": "number", "format": "double"}, "quantidadeMinima": {"type": "number", "format": "double"}, "quantidadeMaxima": {"type": "number", "format": "double"}, "comportamento": {"type": "string", "nullable": true}, "habilitado": {"type": "boolean"}, "idThex": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ModificadorODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Modificador"}, "nullable": true}}, "additionalProperties": false}, "ModificadorServicoPDV": {"type": "object", "properties": {"servicoPDVId": {"type": "string", "format": "uuid"}, "associado": {"type": "boolean"}, "idThex": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NFSe": {"required": ["numNotaFiscal", "serieNotaFiscalId", "status"], "type": "object", "properties": {"status": {"enum": ["Transmitida", "Autorizada", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NaoAutorizada", "Cancelada", "NaoTransmitida", "EmCancelamento", "FalhaNoCancelamento", "FalhaTransmissao", "CanceladaForaPrazo", "InutilizacaoSolicitada", "Inutilizada", "ArquivoTxtGerado"], "type": "string"}, "serieNotaFiscalId": {"type": "string", "format": "uuid"}, "numNotaFiscal": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "NFe": {"required": ["numNotaFiscal", "serieNotaFiscalId", "status"], "type": "object", "properties": {"status": {"enum": ["Transmitida", "Autorizada", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NaoAutorizada", "Cancelada", "NaoTransmitida", "EmCancelamento", "FalhaNoCancelamento", "FalhaTransmissao", "CanceladaForaPrazo", "InutilizacaoSolicitada", "Inutilizada", "ArquivoTxtGerado"], "type": "string"}, "serieNotaFiscalId": {"type": "string", "format": "uuid"}, "numNotaFiscal": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Ncm": {"required": ["codigo", "descricao"], "type": "object", "properties": {"codigo": {"minLength": 1, "type": "string"}, "descricao": {"minLength": 1, "type": "string"}, "aliquotaIPI": {"type": "number", "format": "double"}}, "additionalProperties": false}, "NcmFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NcmODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Ncm"}, "nullable": true}}, "additionalProperties": false}, "OrdemVenda": {"required": ["ajusteEstoqueAutomatico", "clienteId", "data", "dataEntrega", "gera<PERSON><PERSON><PERSON><PERSON>", "geraNotaFiscal", "movimentaEstoque", "nFeRefComplementarIsDevolucao", "numero", "status", "tipoOrdemVenda", "tipoVenda"], "type": "object", "properties": {"numero": {"type": "integer", "format": "int32"}, "numeracaoVolumesTrans": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "marca": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "chaveNFeReferenciada": {"maxLength": 44, "minLength": 0, "type": "string", "nullable": true}, "nFeRefComplementarIsDevolucao": {"type": "boolean"}, "tipoOrdemVenda": {"enum": ["Orcamento", "Pedido", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "string"}, "tipoVenda": {"enum": ["Normal", "Complementar", "<PERSON><PERSON><PERSON>", "Devolucao", "Retorno", "Re<PERSON><PERSON>"], "type": "string"}, "status": {"enum": ["Abe<PERSON>o", "Finalizado", "Cancelado", "NfeCancelada", "Estornado", "PreVenda", "AguardandoNfe", "AguardandoAutorizacaoLimiteCredito", "LimiteCreditoAprovado"], "type": "string"}, "data": {"type": "string", "format": "date-time"}, "clienteId": {"type": "string", "format": "uuid"}, "vendedorId": {"type": "string", "format": "uuid", "nullable": true}, "grupoTributarioPadraoId": {"type": "string", "format": "uuid", "nullable": true}, "transportadoraId": {"type": "string", "format": "uuid", "nullable": true}, "tipoFrete": {"enum": ["CIF", "FOB", "<PERSON><PERSON><PERSON>", "Remetente", "Destinatario", "SemFrete"], "type": "string"}, "tipoEspecie": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "placaVeiculo": {"maxLength": 7, "minLength": 0, "type": "string", "nullable": true}, "estadoPlacaVeiculoId": {"type": "string", "format": "uuid", "nullable": true}, "estadoCodigoIbge": {"type": "string", "nullable": true}, "valorFrete": {"type": "number", "format": "double", "nullable": true}, "somaFreteCIF": {"type": "boolean"}, "pesoBruto": {"type": "number", "format": "double", "nullable": true}, "pesoLiquido": {"type": "number", "format": "double", "nullable": true}, "quantidadeVolumes": {"type": "integer", "format": "int32", "nullable": true}, "formaPagamentoId": {"type": "string", "format": "uuid", "nullable": true}, "condicaoParcelamentoId": {"type": "string", "format": "uuid", "nullable": true}, "categoriaId": {"type": "string", "format": "uuid", "nullable": true}, "dataVencimento": {"type": "string", "format": "date-time", "nullable": true}, "movimentaEstoque": {"type": "boolean"}, "ajusteEstoqueAutomatico": {"type": "boolean"}, "geraFinanceiro": {"type": "boolean"}, "geraNotaFiscal": {"type": "boolean"}, "observacao": {"maxLength": 800, "type": "string", "format": "multiline", "nullable": true}, "total": {"type": "number", "format": "double"}, "totalBruto": {"type": "number", "format": "double"}, "totalRetencoesServicos": {"type": "number", "format": "double", "nullable": true}, "totalImpostosProdutos": {"type": "number", "format": "double", "nullable": true}, "totalImpostosProdutosNaoAgrega": {"type": "number", "format": "double"}, "totalImpostosServicosNaoAgrega": {"type": "number", "format": "double"}, "percentualComissaoVenda": {"type": "number", "format": "double", "nullable": true}, "mensagemPadraoNota": {"maxLength": 4200, "type": "string", "nullable": true}, "informacoesCompletamentaresNFS": {"maxLength": 1000, "type": "string", "nullable": true}, "naturezaOperacao": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "tipoNfeComplementar": {"enum": ["NaoComplementar", "ComplPrecoQtd", "ComplIcms", "ComplIcmsST", "ComplIpi"], "type": "string"}, "contaFinanceiraParcelaPaiIdProdutos": {"type": "string", "format": "uuid", "nullable": true}, "contaFinanceiraParcelaPaiIdServicos": {"type": "string", "format": "uuid", "nullable": true}, "centroCustoId": {"type": "string", "format": "uuid", "nullable": true}, "ufsaidaPaisId": {"type": "string", "format": "uuid", "nullable": true}, "localEmbarque": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "localDespacho": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "endereco": {"type": "string", "nullable": true}, "bairro": {"type": "string", "nullable": true}, "numeroEndereco": {"type": "string", "nullable": true}, "complemento": {"type": "string", "nullable": true}, "cep": {"type": "string", "nullable": true}, "cidadeNFSId": {"type": "string", "format": "uuid", "nullable": true}, "estadoNFSId": {"type": "string", "format": "uuid", "nullable": true}, "tipoTributacaoNFS": {"enum": ["Isencao", "NaoIncidencia", "<PERSON><PERSON><PERSON>", "ExibilidadeSuspeJudicial", "NaoTributavel", "Tributavel", "TributavelFixo", "TributavelSN", "Cancelado", "Extraviado", "MicroEmpreendedor", "ExibilidadeProcessoADM", "SemRecolhimento", "DevidoOutroMun", "IsencaoParcial", "ImunidadeObjetiva"], "type": "string", "nullable": true}, "lojaIntegrada": {"type": "boolean"}, "cidadeCodigoIbge": {"type": "string", "nullable": true}, "dataReabertura": {"type": "string", "format": "date-time", "nullable": true}, "dataEntrega": {"type": "string", "format": "date-time"}, "totalDescontoGeral": {"type": "number", "format": "double"}, "totalDescontoItem": {"type": "number", "format": "double"}, "totalDescontoCabecalho": {"type": "number", "format": "double"}, "percentualDesconto": {"type": "number", "format": "double"}, "tipoCalculoPercValor": {"enum": ["Valor", "Percentual"], "type": "string"}, "tabelaPrecoId": {"type": "string", "format": "uuid", "nullable": true}, "mercadoLivre": {"type": "boolean"}, "totalSeguro": {"type": "number", "format": "double"}, "totalDespesa": {"type": "number", "format": "double"}, "ordemVendaPaiRepeticaoId": {"type": "string", "format": "uuid", "nullable": true}, "repetir": {"type": "boolean"}, "tipoPeriodicidade": {"enum": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mensal", "<PERSON><PERSON>", "Diario"], "type": "string"}, "numeroRepeticoes": {"type": "integer", "format": "int32"}, "turno": {"type": "string", "format": "uuid", "nullable": true}, "geraNfce": {"type": "boolean"}, "valorComissao": {"type": "number", "format": "double"}, "separacaoId": {"type": "string", "format": "uuid", "nullable": true}, "idThex": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrdemVendaFormaPagamento": {"type": "object", "properties": {"formaPagamentoId": {"type": "string", "format": "uuid"}, "condicaoParcelamentoId": {"type": "string", "format": "uuid", "nullable": true}, "categoriaId": {"type": "string", "format": "uuid", "nullable": true}, "centroCustoId": {"type": "string", "format": "uuid", "nullable": true}, "sequencia": {"type": "integer", "format": "int32"}, "percentual": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "bandeira": {"enum": ["NaoInformado", "Visa", "Mastercard", "AmericanExpress", "Sorocred", "DinersClub", "Elo", "<PERSON><PERSON><PERSON>", "<PERSON>ra", "Cabal", "<PERSON><PERSON>", "BanesCard", "CalCard", "Credz", "Discover", "GoodCard", "GreenCard", "<PERSON><PERSON>", "JcB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Policard", "<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "Sodexo", "ValeCard", "Verocheque", "VR", "Ticket", "Outros"], "type": "string"}, "autorizacao": {"type": "string", "nullable": true}, "nsu": {"type": "integer", "format": "int32", "nullable": true}, "nsuRede": {"type": "integer", "format": "int32", "nullable": true}, "numeroCartao": {"type": "string", "nullable": true}, "rede": {"type": "string", "nullable": true}, "numeroCupomFiscal": {"type": "string", "nullable": true}, "pixId": {"type": "string", "nullable": true}, "recebimentoUnico": {"type": "boolean"}, "data": {"type": "string", "format": "date-time"}, "dataVencimento": {"type": "string", "format": "date-time", "nullable": true}, "formaPagamento": {"$ref": "#/components/schemas/FormaPagamentoFiltro"}, "condicaoParcelamento": {"$ref": "#/components/schemas/CondicaoParcelamentoFiltro"}, "categoria": {"$ref": "#/components/schemas/CategoriaFiltro"}}, "additionalProperties": false}, "OrdemVendaFormaPagamentoFiltro": {"type": "object", "properties": {"valor": {"type": "number", "format": "double"}, "bandeira": {"enum": ["NaoInformado", "Visa", "Mastercard", "AmericanExpress", "Sorocred", "DinersClub", "Elo", "<PERSON><PERSON><PERSON>", "<PERSON>ra", "Cabal", "<PERSON><PERSON>", "BanesCard", "CalCard", "Credz", "Discover", "GoodCard", "GreenCard", "<PERSON><PERSON>", "JcB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Policard", "<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "Sodexo", "ValeCard", "Verocheque", "VR", "Ticket", "Outros"], "type": "string"}, "data": {"type": "string", "format": "date-time"}, "dataVencimento": {"type": "string", "format": "date-time", "nullable": true}, "autorizacao": {"type": "string", "nullable": true}, "nsu": {"type": "integer", "format": "int32"}, "nsuRede": {"type": "integer", "format": "int32"}, "numeroCartao": {"type": "string", "nullable": true}, "rede": {"type": "string", "nullable": true}, "numeroCupomFiscal": {"type": "string", "nullable": true}, "pixId": {"type": "string", "nullable": true}, "formaPagamento": {"$ref": "#/components/schemas/FormaPagamentoFiltro"}, "condicaoParcelamento": {"$ref": "#/components/schemas/CondicaoParcelamentoFiltro"}, "categoria": {"$ref": "#/components/schemas/CategoriaFiltro"}}, "additionalProperties": false}, "OrdemVendaFormaPagamentoPost": {"type": "object", "properties": {"ordemVendaId": {"type": "string", "format": "uuid"}, "formasPagamentos": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaFormaPagamento"}, "nullable": true}, "excluirPagamentosAnteriores": {"type": "boolean"}, "geraFinanceiro": {"type": "boolean", "nullable": true}, "geraNotaFiscal": {"type": "boolean"}}, "additionalProperties": false}, "OrdemVendaFormaPagamentoPostODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaFormaPagamentoPost"}, "nullable": true}}, "additionalProperties": false}, "OrdemVendaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVenda"}, "nullable": true}}, "additionalProperties": false}, "OrdemVendaProduto": {"required": ["armazemId", "ordemVendaId", "produtoId"], "type": "object", "properties": {"ordemVendaId": {"type": "string", "format": "uuid"}, "grupoTributarioId": {"type": "string", "format": "uuid", "nullable": true}, "quantidade": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "desconto": {"type": "number", "format": "double"}, "descontoItem": {"type": "number", "format": "double"}, "descontoRateado": {"type": "number", "format": "double"}, "observacao": {"type": "string", "nullable": true}, "produtoId": {"type": "string", "format": "uuid"}, "icms": {"type": "number", "format": "double"}, "fcp": {"type": "number", "format": "double"}, "valorCreditoICMS": {"type": "number", "format": "double"}, "percentualSTRetido": {"type": "number", "format": "double"}, "valorICMSSTRetido": {"type": "number", "format": "double"}, "valorBCSTRetido": {"type": "number", "format": "double"}, "valorICMSSubstituto": {"type": "number", "format": "double"}, "valorFCPSTRetidoAnterior": {"type": "number", "format": "double"}, "valorBCFCPSTRetidoAnterior": {"type": "number", "format": "double"}, "percentualReducaoBC": {"type": "number", "format": "double"}, "percentualReducaoBCST": {"type": "number", "format": "double"}, "pesoLiquido": {"type": "number", "format": "double"}, "pesoBruto": {"type": "number", "format": "double"}, "numeroPedidoCompra": {"maxLength": 15, "minLength": 0, "type": "string", "nullable": true}, "numItemPedidoCompra": {"maxLength": 6, "minLength": 0, "type": "string", "nullable": true}, "baseIpi": {"type": "number", "format": "double"}, "aliquotaIpi": {"type": "number", "format": "double"}, "valorSeguro": {"type": "number", "format": "double"}, "valorDespesa": {"type": "number", "format": "double"}, "diferimentoAliquota": {"type": "number", "format": "double"}, "armazemId": {"type": "string", "format": "uuid"}, "loteId": {"type": "string", "format": "uuid", "nullable": true}, "saldoConsolidado": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrdemVendaProdutoFiltro": {"type": "object", "properties": {"quantidade": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "desconto": {"type": "number", "format": "double"}, "observacao": {"type": "string", "nullable": true}, "grupoTributario": {"$ref": "#/components/schemas/GrupoTributarioFiltro"}, "pesoLiquido": {"type": "number", "format": "double"}, "pesoBruto": {"type": "number", "format": "double"}, "valorSeguro": {"type": "number", "format": "double"}, "valorDespesa": {"type": "number", "format": "double"}, "aliquotaICMS": {"type": "number", "format": "double"}, "baseICMS": {"type": "number", "format": "double"}, "valorICMS": {"type": "number", "format": "double"}, "aliquotaFcp": {"type": "number", "format": "double"}, "baseFcp": {"type": "number", "format": "double"}, "valorFcp": {"type": "number", "format": "double"}, "baseIpi": {"type": "number", "format": "double"}, "aliquotaIpi": {"type": "number", "format": "double"}, "valorIpi": {"type": "number", "format": "double"}, "basePIS": {"type": "number", "format": "double"}, "aliquotaPIS": {"type": "number", "format": "double"}, "valorPIS": {"type": "number", "format": "double"}, "baseCOFINS": {"type": "number", "format": "double"}, "aliquotaCOFINS": {"type": "number", "format": "double"}, "valorCOFINS": {"type": "number", "format": "double"}, "produto": {"$ref": "#/components/schemas/ProdutoFiltro"}, "armazem": {"$ref": "#/components/schemas/ArmazemFiltro"}, "lote": {"$ref": "#/components/schemas/LoteFiltro"}}, "additionalProperties": false}, "OrdemVendaProdutoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaProduto"}, "nullable": true}}, "additionalProperties": false}, "OrdemVendaServico": {"required": ["ordemVendaId", "servicoId"], "type": "object", "properties": {"ordemVendaId": {"type": "string", "format": "uuid"}, "grupoTributarioId": {"type": "string", "format": "uuid", "nullable": true}, "quantidade": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "desconto": {"type": "number", "format": "double"}, "descontoItem": {"type": "number", "format": "double"}, "descontoRateado": {"type": "number", "format": "double"}, "observacao": {"type": "string", "nullable": true}, "servicoId": {"type": "string", "format": "uuid"}, "valorOutrasRetencoes": {"type": "number", "format": "double"}, "descricaoOutrasRetencoes": {"type": "string", "nullable": true}, "isServicoPrioritario": {"type": "boolean"}, "discriminacao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrdemVendaServicoFiltro": {"required": ["servicoId"], "type": "object", "properties": {"quantidade": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "desconto": {"type": "number", "format": "double"}, "observacao": {"type": "string", "nullable": true}, "grupoTributario": {"$ref": "#/components/schemas/GrupoTributarioFiltro"}, "servicoId": {"type": "string", "format": "uuid"}, "valorOutrasRetencoes": {"type": "number", "format": "double"}, "descricaoOutrasRetencoes": {"type": "string", "nullable": true}, "isServicoPrioritario": {"type": "boolean"}, "discriminacao": {"type": "string", "nullable": true}, "servico": {"$ref": "#/components/schemas/ServicoFiltro"}}, "additionalProperties": false}, "OrdemVendaServicoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaServico"}, "nullable": true}}, "additionalProperties": false}, "OrdemVendaServicoPDVFiltro": {"type": "object", "properties": {"quantidade": {"type": "number", "format": "double"}, "valor": {"type": "number", "format": "double"}, "desconto": {"type": "number", "format": "double"}, "observacao": {"type": "string", "nullable": true}, "grupoTributario": {"$ref": "#/components/schemas/GrupoTributarioFiltro"}, "servicoPDVId": {"type": "string", "format": "uuid"}, "servicoPDV": {"$ref": "#/components/schemas/ServicoPDVFiltro"}}, "additionalProperties": false}, "Pais": {"required": ["codigoBacen", "codigoIbge", "codigoSiscomex", "nome", "sigla"], "type": "object", "properties": {"nome": {"maxLength": 50, "minLength": 0, "type": "string"}, "codigoIbge": {"maxLength": 3, "minLength": 0, "type": "string"}, "codigoBacen": {"maxLength": 4, "minLength": 0, "type": "string"}, "codigoSiscomex": {"maxLength": 3, "minLength": 0, "type": "string"}, "sigla": {"maxLength": 3, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "PaisFiltro": {"required": ["codigoIbge"], "type": "object", "properties": {"nome": {"maxLength": 35, "minLength": 0, "type": "string", "nullable": true}, "codigoIbge": {"maxLength": 3, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "PaisODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Pais"}, "nullable": true}}, "additionalProperties": false}, "ParametroTributario": {"type": "object", "properties": {"simplesNacional": {"type": "boolean"}, "aliquotaSimplesNacional": {"type": "number", "format": "double"}, "aliquotaICMS": {"type": "number", "format": "double"}, "aliquotaISS": {"type": "number", "format": "double"}, "aliquotaPISPASEP": {"type": "number", "format": "double"}, "aliquotaCOFINS": {"type": "number", "format": "double"}, "aliquotaCSLL": {"type": "number", "format": "double"}, "aliquotaINSS": {"type": "number", "format": "double"}, "aliquotaImpostoRenda": {"type": "number", "format": "double"}, "registroSimplificadoMT": {"type": "boolean"}, "mensagemPadraoNota": {"type": "string", "nullable": true}, "numeroRetornoNF": {"type": "string", "nullable": true}, "tipoAmbiente": {"enum": ["Con<PERSON>gu<PERSON><PERSON>", "Producao", "Homologacao"], "type": "string"}, "tipoVersaoNFe": {"enum": ["v4"], "type": "string"}, "tipoModalidade": {"enum": ["Normal", "FSIA", "DPEC", "FSDA", "SVCAN", "SVCRS", "NFCe"], "type": "string"}, "aliquotaFCP": {"type": "number", "format": "double"}, "tipoPresencaComprador": {"enum": ["NaoSeAplica", "Presencial", "Internet", "TeleAtendimento", "EntregaDomicilio", "Outros"], "type": "string"}, "horarioVerao": {"enum": ["<PERSON>m", "<PERSON><PERSON>"], "type": "string"}, "tipoHorario": {"enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Brasilia", "Manaus"], "type": "string"}, "tipoCRT": {"enum": ["SimplesNacional", "ExcessoSublimiteDeReceitaBruta", "RegimeNormal", "SimplesNacionalMEI"], "type": "string"}, "periodoAnoDocLGPD": {"type": "integer", "format": "int32"}, "versaoNFSe": {"type": "string", "nullable": true}, "tipoAmbienteNFS": {"enum": ["Con<PERSON>gu<PERSON><PERSON>", "Producao", "Homologacao"], "type": "string"}, "incentivoCultura": {"type": "boolean"}, "usuarioWebServer": {"type": "string", "nullable": true}, "senhaWebServer": {"type": "string", "nullable": true}, "chaveAutenticacao": {"type": "string", "nullable": true}, "autorizacao": {"type": "string", "nullable": true}, "tipoTributacaoNFS": {"enum": ["Isencao", "NaoIncidencia", "<PERSON><PERSON><PERSON>", "ExibilidadeSuspeJudicial", "NaoTributavel", "Tributavel", "TributavelFixo", "TributavelSN", "Cancelado", "Extraviado", "MicroEmpreendedor", "ExibilidadeProcessoADM", "SemRecolhimento", "DevidoOutroMun", "IsencaoParcial", "ImunidadeObjetiva"], "type": "string"}, "formatarCodigoISS": {"type": "boolean"}, "tipoRegimeEspecialTributacao": {"enum": ["TributacaoNormal", "MicroEmpresaMunicipal", "Estimativa", "SociedadeProfissionais", "Cooperativa", "MEI", "MEEPP", "MovimentoMensal", "EIRELI", "EmpresaIndividual", "EPP", "Microempresario", "Outros", "<PERSON><PERSON><PERSON>", "NotaAvulsa", "NaoSeEnquandraAnteriores"], "type": "string"}, "tipoVersaoTSS": {"enum": ["v278", "v300"], "type": "string"}, "abateImpostoFinanceiro": {"type": "boolean"}, "tipoDescricaoProduto": {"enum": ["Tipo1", "Tipo2", "Tipo3", "Tipo4", "Tipo5", "Tipo6", "Tipo7", "Tipo8", "Tipo9", "Tipo10", "Tipo11", "Tipo12", "Tipo13", "Tipo14", "Tipo15"], "type": "string"}, "urlTSS": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ParametroTributarioODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/ParametroTributario"}, "nullable": true}}, "additionalProperties": false}, "Pessoa": {"required": ["nome"], "type": "object", "properties": {"nome": {"maxLength": 180, "minLength": 0, "type": "string"}, "cpfcnpj": {"maxLength": 14, "minLength": 0, "type": "string", "nullable": true}, "cep": {"maxLength": 8, "minLength": 0, "type": "string", "nullable": true}, "endereco": {"maxLength": 180, "minLength": 0, "type": "string", "nullable": true}, "numero": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "complemento": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "bairro": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "cidadeId": {"type": "string", "format": "uuid", "nullable": true}, "estadoId": {"type": "string", "format": "uuid", "nullable": true}, "telefone": {"maxLength": 15, "minLength": 0, "type": "string", "nullable": true}, "celular": {"maxLength": 15, "minLength": 0, "type": "string", "nullable": true}, "contato": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "observacao": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "nomeComercial": {"maxLength": 180, "minLength": 0, "type": "string", "nullable": true}, "inscricaoEstadual": {"maxLength": 18, "minLength": 0, "type": "string", "nullable": true}, "inscricaoMunicipal": {"maxLength": 18, "minLength": 0, "type": "string", "nullable": true}, "consumidorFinal": {"type": "boolean", "default": false}, "transportadora": {"type": "boolean", "default": false}, "cliente": {"type": "boolean", "default": false}, "fornecedor": {"type": "boolean", "default": false}, "vendedor": {"type": "boolean", "default": false}, "percentualComissao": {"type": "number", "format": "double"}, "tipoIndicacaoInscricaoEstadual": {"enum": ["ContribuinteICMS", "ContribuinteIsento", "NaoContribuinte"], "type": "string"}, "paisId": {"type": "string", "format": "uuid", "nullable": true}, "idEstrangeiro": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "dataNascimento": {"type": "string", "format": "date-time", "nullable": true}, "situacaoEspecialNFS": {"enum": ["Outro", "SUS", "Executivo", "Bancos", "ComercioIndustria", "LegislativoJudiciario"], "type": "string"}, "formaPagamentoId": {"type": "string", "format": "uuid", "nullable": true}, "tabelaPrecoId": {"type": "string", "format": "uuid", "nullable": true}, "condicaoParcelamentoId": {"type": "string", "format": "uuid", "nullable": true}, "limiteCredito": {"type": "number", "format": "double"}, "inscricaoSuframa": {"maxLength": 9, "minLength": 0, "type": "number", "format": "double"}, "condicaoParcelamento": {"$ref": "#/components/schemas/CondicaoParcelamentoFiltro"}, "formaPagamento": {"$ref": "#/components/schemas/FormaPagamentoFiltro"}, "cidade": {"$ref": "#/components/schemas/CidadeFiltro"}, "estado": {"$ref": "#/components/schemas/CidadeFiltro"}, "pais": {"$ref": "#/components/schemas/PaisFiltro"}}, "additionalProperties": false}, "PessoaFiltro": {"required": ["cpfcnpj", "nome"], "type": "object", "properties": {"nome": {"minLength": 1, "type": "string"}, "nomeComercial": {"maxLength": 180, "minLength": 0, "type": "string", "nullable": true}, "cpfcnpj": {"minLength": 1, "type": "string"}, "observacao": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "inscricaoEstadual": {"maxLength": 18, "minLength": 0, "type": "string", "nullable": true}, "inscricaoMunicipal": {"maxLength": 18, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "PessoaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Pessoa"}, "nullable": true}}, "additionalProperties": false}, "PlanoConta": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"minLength": 1, "type": "string"}, "planoContaPaiId": {"type": "string", "format": "uuid", "nullable": true}, "tipoPlanoConta": {"enum": ["Ativo", "Passivo", "ContasResultado"], "type": "string"}}, "additionalProperties": false}, "PlanoContaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/PlanoConta"}, "nullable": true}}, "additionalProperties": false}, "Produto": {"required": ["departamentoSecaoId", "descricao", "unidadeMedidaId"], "type": "object", "properties": {"descricao": {"maxLength": 200, "minLength": 0, "type": "string"}, "grupoProdutoId": {"type": "string", "format": "uuid", "nullable": true}, "unidadeMedidaId": {"type": "string", "format": "uuid"}, "departamentoSecaoId": {"type": "string", "format": "uuid"}, "objetoDeManutencao": {"enum": ["<PERSON><PERSON>", "<PERSON>m"], "type": "string"}, "codigoProduto": {"type": "string", "nullable": true}, "codigoBarras": {"type": "string", "nullable": true}, "valorVenda": {"type": "number", "format": "double"}, "valorCusto": {"type": "number", "format": "double"}, "ncmId": {"type": "string", "format": "uuid", "nullable": true}, "codigoNcm": {"type": "string", "nullable": true}, "enquadramentoLegalIPIId": {"type": "string", "format": "uuid", "nullable": true}, "codigoEnquadramentoLegalIPI": {"type": "string", "nullable": true}, "observacao": {"type": "string", "nullable": true}, "tipoProduto": {"enum": ["Insumo", "ProdutoFinal", "<PERSON><PERSON><PERSON>", "Outros", "ProdutoIntermediario", "MateriaPrima", "<PERSON><PERSON><PERSON>"], "type": "string"}, "cestId": {"type": "string", "format": "uuid", "nullable": true}, "codigoCest": {"type": "string", "nullable": true}, "origemMercadoria": {"enum": ["Nacional", "EstrangeiraImportacaoDireta", "EstrangeiraAdquiridaMercadoInterno", "NacionalConteudoImportacaoEntre40e70", "NacionalProducaoEmConformidade", "NacionalConteudoImportacaoInferior40", "EstrangeiraImportacaoDiretaSemSimilarNacional", "EstrangeiraAdquiridaNoMercadoInterno", "NacionalConteudoImportacaoSuperior70"], "type": "string"}, "pesoBruto": {"type": "number", "format": "double"}, "pesoLiquido": {"type": "number", "format": "double"}, "fabricante": {"type": "string", "nullable": true}, "marca": {"type": "string", "nullable": true}, "tipoVarianteProduto": {"enum": ["NaoSeAplica", "ProdutoPai", "ProdutoFil<PERSON>"], "type": "string"}, "produtoPai": {"type": "boolean", "default": false}, "controlaLote": {"type": "boolean", "default": false}, "codigoAnp": {"type": "string", "nullable": true}, "descricaoAnp": {"type": "string", "nullable": true}, "modelo": {"type": "string", "nullable": true}, "cor": {"type": "string", "nullable": true}, "usado": {"type": "boolean", "default": false}, "vendavel": {"type": "boolean", "default": false}, "abreviacaoUnidadeMedida": {"type": "string", "nullable": true}, "aliquotaIpi": {"type": "number", "format": "double"}, "aliquotaPis": {"type": "number", "format": "double"}, "aliquotaCofins": {"type": "number", "format": "double"}, "EXTIPI": {"maxLength": 3, "minLength": 0, "type": "string", "nullable": true}, "percentualComissao": {"type": "number", "format": "double"}, "codigoRegistroAnvisa": {"type": "string", "nullable": true}, "lancamentoVariavel": {"type": "boolean"}, "couvert": {"type": "boolean"}, "descricaoAbreviada": {"type": "string", "nullable": true}, "descricaoCompleta": {"type": "string", "nullable": true}, "gruposProdutoPOS": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProdutoPOS"}, "nullable": true}, "modificadores": {"type": "array", "items": {"$ref": "#/components/schemas/Modificador"}, "nullable": true}, "produtosServicosPDV": {"type": "array", "items": {"$ref": "#/components/schemas/ProdutoServicoPDV"}, "nullable": true}, "grupoTributario": {"$ref": "#/components/schemas/GrupoTributario"}, "saldoProdutoArmazem": {"$ref": "#/components/schemas/SaldoProdutoArmazem"}}, "additionalProperties": false}, "ProdutoBase": {"required": ["departamentoSecaoId", "descricao", "unidadeMedidaId"], "type": "object", "properties": {"descricao": {"maxLength": 200, "minLength": 0, "type": "string"}, "grupoProdutoId": {"type": "string", "format": "uuid", "nullable": true}, "unidadeMedidaId": {"type": "string", "format": "uuid"}, "departamentoSecaoId": {"type": "string", "format": "uuid"}, "objetoDeManutencao": {"enum": ["<PERSON><PERSON>", "<PERSON>m"], "type": "string"}, "codigoProduto": {"type": "string", "nullable": true}, "codigoBarras": {"type": "string", "nullable": true}, "valorVenda": {"type": "number", "format": "double"}, "valorCusto": {"type": "number", "format": "double"}, "ncmId": {"type": "string", "format": "uuid", "nullable": true}, "codigoNcm": {"type": "string", "nullable": true}, "enquadramentoLegalIPIId": {"type": "string", "format": "uuid", "nullable": true}, "codigoEnquadramentoLegalIPI": {"type": "string", "nullable": true}, "observacao": {"type": "string", "nullable": true}, "tipoProduto": {"enum": ["Insumo", "ProdutoFinal", "<PERSON><PERSON><PERSON>", "Outros", "ProdutoIntermediario", "MateriaPrima", "<PERSON><PERSON><PERSON>"], "type": "string"}, "cestId": {"type": "string", "format": "uuid", "nullable": true}, "codigoCest": {"type": "string", "nullable": true}, "origemMercadoria": {"enum": ["Nacional", "EstrangeiraImportacaoDireta", "EstrangeiraAdquiridaMercadoInterno", "NacionalConteudoImportacaoEntre40e70", "NacionalProducaoEmConformidade", "NacionalConteudoImportacaoInferior40", "EstrangeiraImportacaoDiretaSemSimilarNacional", "EstrangeiraAdquiridaNoMercadoInterno", "NacionalConteudoImportacaoSuperior70"], "type": "string"}, "pesoBruto": {"type": "number", "format": "double"}, "pesoLiquido": {"type": "number", "format": "double"}, "fabricante": {"type": "string", "nullable": true}, "marca": {"type": "string", "nullable": true}, "tipoVarianteProduto": {"enum": ["NaoSeAplica", "ProdutoPai", "ProdutoFil<PERSON>"], "type": "string"}, "produtoPai": {"type": "boolean", "default": false}, "controlaLote": {"type": "boolean", "default": false}, "codigoAnp": {"type": "string", "nullable": true}, "descricaoAnp": {"type": "string", "nullable": true}, "modelo": {"type": "string", "nullable": true}, "cor": {"type": "string", "nullable": true}, "usado": {"type": "boolean", "default": false}, "vendavel": {"type": "boolean", "default": false}, "abreviacaoUnidadeMedida": {"type": "string", "nullable": true}, "aliquotaIpi": {"type": "number", "format": "double"}, "aliquotaPis": {"type": "number", "format": "double"}, "aliquotaCofins": {"type": "number", "format": "double"}, "EXTIPI": {"maxLength": 3, "minLength": 0, "type": "string", "nullable": true}, "percentualComissao": {"type": "number", "format": "double"}, "codigoRegistroAnvisa": {"type": "string", "nullable": true}, "lancamentoVariavel": {"type": "boolean"}, "couvert": {"type": "boolean"}, "descricaoAbreviada": {"type": "string", "nullable": true}, "descricaoCompleta": {"type": "string", "nullable": true}, "gruposProdutoPOS": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProdutoPOS"}, "nullable": true}, "modificadores": {"type": "array", "items": {"$ref": "#/components/schemas/Modificador"}, "nullable": true}, "produtosServicosPDV": {"type": "array", "items": {"$ref": "#/components/schemas/ProdutoServicoPDV"}, "nullable": true}}, "additionalProperties": false}, "ProdutoFiltro": {"type": "object", "properties": {"codigoProduto": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProdutoPOS": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "ProdutoPostGet": {"required": ["departamentoSecaoId", "descricao", "unidadeMedidaId"], "type": "object", "properties": {"descricao": {"maxLength": 200, "minLength": 0, "type": "string"}, "grupoProdutoId": {"type": "string", "format": "uuid", "nullable": true}, "unidadeMedidaId": {"type": "string", "format": "uuid"}, "departamentoSecaoId": {"type": "string", "format": "uuid"}, "objetoDeManutencao": {"enum": ["<PERSON><PERSON>", "<PERSON>m"], "type": "string"}, "codigoProduto": {"type": "string", "nullable": true}, "codigoBarras": {"type": "string", "nullable": true}, "valorVenda": {"type": "number", "format": "double"}, "valorCusto": {"type": "number", "format": "double"}, "ncmId": {"type": "string", "format": "uuid", "nullable": true}, "codigoNcm": {"type": "string", "nullable": true}, "enquadramentoLegalIPIId": {"type": "string", "format": "uuid", "nullable": true}, "codigoEnquadramentoLegalIPI": {"type": "string", "nullable": true}, "observacao": {"type": "string", "nullable": true}, "tipoProduto": {"enum": ["Insumo", "ProdutoFinal", "<PERSON><PERSON><PERSON>", "Outros", "ProdutoIntermediario", "MateriaPrima", "<PERSON><PERSON><PERSON>"], "type": "string"}, "cestId": {"type": "string", "format": "uuid", "nullable": true}, "codigoCest": {"type": "string", "nullable": true}, "origemMercadoria": {"enum": ["Nacional", "EstrangeiraImportacaoDireta", "EstrangeiraAdquiridaMercadoInterno", "NacionalConteudoImportacaoEntre40e70", "NacionalProducaoEmConformidade", "NacionalConteudoImportacaoInferior40", "EstrangeiraImportacaoDiretaSemSimilarNacional", "EstrangeiraAdquiridaNoMercadoInterno", "NacionalConteudoImportacaoSuperior70"], "type": "string"}, "pesoBruto": {"type": "number", "format": "double"}, "pesoLiquido": {"type": "number", "format": "double"}, "fabricante": {"type": "string", "nullable": true}, "marca": {"type": "string", "nullable": true}, "tipoVarianteProduto": {"enum": ["NaoSeAplica", "ProdutoPai", "ProdutoFil<PERSON>"], "type": "string"}, "produtoPai": {"type": "boolean", "default": false}, "controlaLote": {"type": "boolean", "default": false}, "codigoAnp": {"type": "string", "nullable": true}, "descricaoAnp": {"type": "string", "nullable": true}, "modelo": {"type": "string", "nullable": true}, "cor": {"type": "string", "nullable": true}, "usado": {"type": "boolean", "default": false}, "vendavel": {"type": "boolean", "default": false}, "abreviacaoUnidadeMedida": {"type": "string", "nullable": true}, "aliquotaIpi": {"type": "number", "format": "double"}, "aliquotaPis": {"type": "number", "format": "double"}, "aliquotaCofins": {"type": "number", "format": "double"}, "EXTIPI": {"maxLength": 3, "minLength": 0, "type": "string", "nullable": true}, "percentualComissao": {"type": "number", "format": "double"}, "codigoRegistroAnvisa": {"type": "string", "nullable": true}, "lancamentoVariavel": {"type": "boolean"}, "couvert": {"type": "boolean"}, "descricaoAbreviada": {"type": "string", "nullable": true}, "descricaoCompleta": {"type": "string", "nullable": true}, "gruposProdutoPOS": {"type": "array", "items": {"$ref": "#/components/schemas/GrupoProdutoPOS"}, "nullable": true}, "modificadores": {"type": "array", "items": {"$ref": "#/components/schemas/Modificador"}, "nullable": true}, "produtosServicosPDV": {"type": "array", "items": {"$ref": "#/components/schemas/ProdutoServicoPDV"}, "nullable": true}, "unidadeMedida": {"$ref": "#/components/schemas/UnidadeMedidaFiltro"}, "ncm": {"$ref": "#/components/schemas/NcmFiltro"}, "cest": {"$ref": "#/components/schemas/CestFiltro"}, "enquadramentoLegalIPI": {"$ref": "#/components/schemas/EnquadramentoLegalIPIFiltro"}, "departamentoSetor": {"$ref": "#/components/schemas/DepartamentoSetorFiltro"}}, "additionalProperties": false}, "ProdutoPostGetODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/ProdutoPostGet"}, "nullable": true}}, "additionalProperties": false}, "ProdutoServicoPDV": {"type": "object", "properties": {"produtoId": {"type": "string", "format": "uuid"}, "servicoPDVId": {"type": "string", "format": "uuid"}, "precoVenda": {"type": "number", "format": "double"}, "disponivel": {"type": "boolean"}, "servicoPDV": {"$ref": "#/components/schemas/ServicoPDV"}}, "additionalProperties": false}, "Produtos": {"required": ["itens"], "type": "object", "properties": {"itens": {"type": "array", "items": {"$ref": "#/components/schemas/Produto"}}}, "additionalProperties": false}, "ReabrirPedido": {"required": ["estornado", "id", "reabrirProximasRecorrencias"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "reabrirProximasRecorrencias": {"type": "boolean"}, "estornado": {"type": "boolean"}, "cancelado": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "ResponseError": {"type": "object", "properties": {"error": {"$ref": "#/components/schemas/Error"}}, "additionalProperties": false}, "ResponseInnerMessage": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32"}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SaldoProdutoArmazem": {"type": "object", "properties": {"armazemId": {"type": "string", "format": "uuid"}, "produtoId": {"type": "string", "format": "uuid"}, "saldoMinimo": {"type": "number", "format": "double"}}, "additionalProperties": false}, "SerieNotaFiscal": {"type": "object", "properties": {"serie": {"type": "string", "nullable": true}, "tipoOperacaoSerieNotaFiscal": {"enum": ["NFe", "NFSe", "Ambas", "NFCe"], "type": "string"}, "numNotaFiscal": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SerieNotaFiscalODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/SerieNotaFiscal"}, "nullable": true}}, "additionalProperties": false}, "Servico": {"type": "object", "properties": {"codigoServico": {"type": "string", "nullable": true}, "descricao": {"type": "string", "nullable": true}, "nbsId": {"type": "string", "format": "uuid", "nullable": true}, "issId": {"type": "string", "format": "uuid", "nullable": true}, "codigoTributacaoMunicipal": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "valorServico": {"type": "number", "format": "double"}, "observacao": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "codigoIssEspecifico": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "codigoFiscalPrestacao": {"maxLength": 5, "minLength": 0, "type": "string", "nullable": true}, "codigoIss": {"type": "string", "nullable": true}, "codigoNbs": {"type": "string", "nullable": true}, "abreviacaoUnidadeMedida": {"type": "string", "nullable": true}, "unidadeMedidaId": {"type": "string", "format": "uuid", "nullable": true}, "mercadoLivre": {"type": "boolean"}, "descriminacao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServicoFiltro": {"type": "object", "properties": {"codigoServico": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServicoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Ser<PERSON>o"}, "nullable": true}}, "additionalProperties": false}, "ServicoPDV": {"required": ["descricao"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "descricao": {"maxLength": 180, "minLength": 0, "type": "string"}, "codigo": {"type": "string", "nullable": true}, "idThex": {"type": "string", "nullable": true}, "origem": {"enum": ["Eleve", "PMS", "POS"], "type": "string"}}, "additionalProperties": false}, "ServicoPDVFiltro": {"type": "object", "properties": {"codigo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServicoPDVODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/ServicoPDV"}, "nullable": true}}, "additionalProperties": false}, "SubstituicaoTributaria": {"type": "object", "properties": {"ncmId": {"type": "string", "format": "uuid"}, "estadoOrigemId": {"type": "string", "format": "uuid"}, "estadoDestinoId": {"type": "string", "format": "uuid"}, "mva": {"type": "number", "format": "double"}, "tipoSubstituicaoTributaria": {"enum": ["Entrada", "<PERSON><PERSON>"], "type": "string"}, "cestId": {"type": "string", "format": "uuid", "nullable": true}, "fcp": {"type": "number", "format": "double"}, "aliquotaIntraEstadual": {"type": "number", "format": "double"}, "aliquotaInterEstadual": {"type": "number", "format": "double"}, "estadoOrigemCodigoIbge": {"type": "string", "nullable": true}, "estadoDestinoCodigoIbge": {"type": "string", "nullable": true}, "codigoNcm": {"type": "string", "nullable": true}, "codigoCest": {"type": "string", "nullable": true}, "icmsPauta": {"type": "number", "format": "double"}}, "additionalProperties": false}, "SubstituicaoTributariaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/SubstituicaoTributaria"}, "nullable": true}}, "additionalProperties": false}, "TabelaPreco": {"required": ["descricao"], "type": "object", "properties": {"descricao": {"maxLength": 200, "minLength": 0, "type": "string"}, "dataInicio": {"type": "string", "format": "date-time", "nullable": true}, "dataFim": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TabelaPrecoODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/TabelaPreco"}, "nullable": true}}, "additionalProperties": false}, "UnidadeMedida": {"type": "object", "properties": {"abreviacao": {"type": "string", "nullable": true}, "descricao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UnidadeMedidaFiltro": {"type": "object", "properties": {"abreviacao": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UnidadeMedidaODataListResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/UnidadeMedida"}, "nullable": true}}, "additionalProperties": false}, "Venda": {"required": ["ajusteEstoqueAutomatico", "gera<PERSON><PERSON><PERSON><PERSON>", "geraNotaFiscal", "movimentaEstoque"], "type": "object", "properties": {"numero": {"type": "integer", "format": "int32"}, "data": {"type": "string", "format": "date-time"}, "pesoBruto": {"type": "number", "format": "double", "nullable": true}, "pesoLiquido": {"type": "number", "format": "double", "nullable": true}, "desconto": {"type": "number", "format": "double"}, "quantidadeVolumes": {"type": "integer", "format": "int32", "nullable": true}, "movimentaEstoque": {"type": "boolean"}, "ajusteEstoqueAutomatico": {"type": "boolean"}, "geraFinanceiro": {"type": "boolean"}, "geraNotaFiscal": {"type": "boolean"}, "observacao": {"maxLength": 800, "type": "string", "format": "multiline", "nullable": true}, "mensagemPadraoNota": {"maxLength": 4200, "type": "string", "nullable": true}, "naturezaOperacao": {"maxLength": 60, "minLength": 0, "type": "string", "nullable": true}, "finalizar": {"type": "boolean"}, "idThex": {"type": "string", "nullable": true}, "cliente": {"$ref": "#/components/schemas/PessoaFiltro"}, "vendedor": {"$ref": "#/components/schemas/PessoaFiltro"}, "formasDePagamento": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaFormaPagamentoFiltro"}, "nullable": true}, "produtos": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaProdutoFiltro"}, "nullable": true}, "servicos": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaServicoFiltro"}, "nullable": true}, "servicosPDV": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaServicoPDVFiltro"}, "nullable": true}}, "additionalProperties": false}, "VendaAtualizacaoImpostos": {"type": "object", "properties": {"numero": {"type": "integer", "format": "int32"}, "produtos": {"type": "array", "items": {"$ref": "#/components/schemas/OrdemVendaProdutoFiltro"}, "nullable": true}}, "additionalProperties": false}, "VendaResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "numero": {"type": "integer", "format": "int32"}, "total": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Vendas": {"type": "object", "properties": {"itens": {"type": "array", "items": {"$ref": "#/components/schemas/Venda"}, "nullable": true}}, "additionalProperties": false}, "VendasResponse": {"type": "object", "properties": {"mensagem": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "Adquirente", "description": "Controle os seus adquirentes."}, {"name": "Armazem", "description": "Controle os seus espaços físicos de armazenagem através da rotina de cadastro de armazém de estoque."}, {"name": "CalculaTotalNotaFiscal", "description": "Relacionados ao CalculaTotalNotaFiscal"}, {"name": "CalculaTotalOrdemVenda", "description": "Consulte os valores totais do pedido de venda."}, {"name": "Categoria", "description": "Organize e classifique suas receitas e despesas através da rotina de cadastro de categorias financeiras."}, {"name": "CentroCusto", "description": "Faça sua gestão financeira dividindo internamente sua operação em setores, através da rotina de cadastro de centro de custo."}, {"name": "CertificadoDigital", "description": "Relacionados ao Certificado Digital"}, {"name": "Cest", "description": "Consulta livre do Código Especificador da Substituição Tributária."}, {"name": "Cfop", "description": "CFOP"}, {"name": "Cidade", "description": "Consulte o Id de todas as cidades disponíveis para amarração nos demais cadastros."}, {"name": "CondicaoParcelamento", "description": "Estruture as condições para parcelamento de pagamentos e recebimentos do seu negócio através da rotina de cadastro de condição de parcelamento."}, {"name": "Departamento", "description": "Faça a gestão de informações técnicas e fiscais dos seus itens através da rotina de cadastro de departamentos."}, {"name": "DepartamentoSetor", "description": "Faça a gestão de informações técnicas e fiscais dos seus itens através da rotina de cadastro de seção dos departamentos."}, {"name": "Difal", "description": "Relacionados ao Difal"}, {"name": "EnquadramentoLegalIpi", "description": "Em caso de obrigação fiscal, consulte o enquadramento legal de IPI correto para ser associado à finalidade e ao destino da operação de sua nota fiscal."}, {"name": "Estado", "description": "Consulte o Id de todos os Estados Federados disponíveis para amarração nos demais cadastros."}, {"name": "FormaPagamento", "description": "Organize os seus meios de pagamento e recebimento através da rotina de cadastro de formas de pagamento."}, {"name": "FormaPagamentoTaxaCartao", "description": "Organize os seus meios de pagamento e recebimento através da rotina de cadastro de formas de pagamento."}, {"name": "GerenciamentoFormaPagamentoTaxaCartao", "description": "Gerenciamento de Taxas nas Formas de Pagamento."}, {"name": "GrupoProduto", "description": "Classifique seus produtos que são do mesmo tipo ou possuem aplicações semelhantes através da rotina de cadastro de grupo de produtos."}, {"name": "GrupoProdutoPOS", "description": "Classifique seus produtos que são do mesmo tipo ou possuem aplicações semelhantes através da rotina de cadastro de grupo de produtos."}, {"name": "GruposTributarios", "description": "Faça a gestão em massa de informações tributárias"}, {"name": "GrupoTributario", "description": "faturamento > Grupo tributário"}, {"name": "Modificador", "description": "Gerencie os modificadores utilizados nos produtos."}, {"name": "Ncm", "description": "Consulta livre da Nomenclatura Comum do Mercosul."}, {"name": "NFe", "description": "Transmita para a Sefaz as suas notas fiscais através da rotina NFe."}, {"name": "NFeProduto", "description": "Consulte os produtos que foram incluídos na nota fiscal (NFe)."}, {"name": "NFSe", "description": "Transmita para a Sefaz as suas notas fiscais através da rotina NFSe."}, {"name": "NFSeServico", "description": "Consulte os serviços que foram incluídos na nota fiscal de serviço (NFSe)."}, {"name": "NotaFiscalAtualizaStatus", "description": "Utilize esta rota para atualizar o status de suas notas fiscais junto à Sefaz."}, {"name": "NotaFiscalCancelar", "description": "Para casos onde a mercadoria ainda não foi circulada e o prazo ainda está vigente, utilize esta rota para cancelamento das notas fiscais emitidas."}, {"name": "NotaFiscalCartaCorrecaoAtualizaStatus", "description": "Atualize e acompanhe o status da correção de suas notas junto à Sefaz."}, {"name": "NotaFiscalCartaCorrecao", "description": "Esta rotina possibilita a criação de um evento para corrigir as informações da NF-e."}, {"name": "NotaFiscal", "description": "Consulte todos os dados de suas notas fiscais emitidas."}, {"name": "NotaFiscalTributacao", "description": "CFOP"}, {"name": "OrdemVenda", "description": "Informe todos os dados de seu pedido de venda nesta rota. Lembrando que os itens de venda (OrdemVendaProduto / OrdemVendaServico) deverão referenciar o Id deste cabeçalho."}, {"name": "OrdemVendaFormaPagamento", "description": "Esta rota deverá ser utilizada em casos onde há mais de uma condição de pagamento para o mesmo pedido."}, {"name": "OrdemVendaProduto", "description": "Informe todos os produtos que compõem o seu pedido de venda."}, {"name": "OrdemVenda<PERSON><PERSON>", "description": "Informe todos os serviços que compõem o seu pedido de venda."}, {"name": "<PERSON><PERSON>", "description": "Consulte o Id de todos os países disponíveis para amarração nos demais cadastros."}, {"name": "ParametroTributario", "description": "faturamento > Parâmetro tributário"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Controle os dados de seus clientes, fornecedores, transportadores e vendedores, através da rotina de cadastro de pessoas."}, {"name": "PlanoConta", "description": "Cadastro de planoContas."}, {"name": "Produ<PERSON>", "description": "Faça a gestão de informações técnicas e fiscais dos seus itens através da rotina de cadastro de produtos."}, {"name": "<PERSON><PERSON><PERSON>", "description": "Faça a gestão em massa de informações técnicas e fiscais dos seus itens através da rotina de cadastro de produtos."}, {"name": "ReabrirPedido", "description": "Faça a reabertura de um pedido de venda."}, {"name": "SerieNotaFiscal", "description": "Relacionados ao SerieNotaFiscal"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Tenha total controle sobre os serviços aos quais a sua empresa oferece através da rotina de cadastro de serviços."}, {"name": "ServicoPDV", "description": "Gerencie os serviços utilizados em pontos de vendas."}, {"name": "SubstituicaoTributaria", "description": "Relacionados ao Substituição Tributária"}, {"name": "TabelaPreco", "description": "Padronize a precificação de seus produtos e serviços através da rotina de cadastro de tabela de preço."}, {"name": "UnidadeMedida", "description": "Controle to<PERSON> as unidades de medida utilizadas nas movimentações de produtos e serviços de seu negócio através da rotina de cadastro de unidades de medida."}, {"name": "<PERSON><PERSON><PERSON>", "description": "Informe todos os dados da sua venda nesta rota."}, {"name": "<PERSON><PERSON><PERSON>", "description": "Informe todos os dados das suas vendas nesta rota."}]}